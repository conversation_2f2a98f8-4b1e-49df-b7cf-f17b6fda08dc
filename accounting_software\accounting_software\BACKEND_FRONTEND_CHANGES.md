# Backend and Frontend Changes for Odoo Sales Methodology

## 🔧 **Backend Changes Required**

### **1. Sales Order Model Updates** (`erp_backend/sales/models.py`)

#### **Add Odoo-style Fields to SalesOrder Model:**
```python
class SalesOrder(models.Model):
    # Add these new fields to existing model
    
    # Odoo-style states (replace current STATUS_CHOICES)
    STATE_CHOICES = [
        ('draft', 'Quotation'),
        ('sent', 'Quotation Sent'),
        ('sale', 'Sales Order'),
        ('done', 'Locked'),
        ('cancel', 'Cancelled'),
    ]
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')
    
    # Invoice Policy
    INVOICE_POLICY_CHOICES = [
        ('order', 'Ordered quantities'),
        ('delivery', 'Delivered quantities'),
    ]
    invoice_policy = models.CharField(max_length=20, choices=INVOICE_POLICY_CHOICES, default='order')
    
    # Delivery Policy
    DELIVERY_POLICY_CHOICES = [
        ('direct', 'As soon as possible'),
        ('one', 'When all products are ready'),
    ]
    delivery_policy = models.CharField(max_length=20, choices=DELIVERY_POLICY_CHOICES, default='direct')
    
    # Odoo-style naming
    name = models.CharField(max_length=100, unique=True)  # SO number
    partner_id = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True)  # Customer
    date_order = models.DateField()  # Order date
    validity_date = models.DateField(blank=True, null=True)  # Quotation expiry
    commitment_date = models.DateField(blank=True, null=True)  # Delivery commitment
    
    # Financial tracking
    amount_untaxed = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_tax = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_invoiced = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_to_invoice = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Document counts
    delivery_count = models.IntegerField(default=0)
    invoice_count = models.IntegerField(default=0)
    
    # Additional Odoo fields
    client_order_ref = models.CharField(max_length=100, blank=True, null=True)
    note = models.TextField(blank=True, null=True)  # Terms and conditions
    payment_term_id = models.ForeignKey('PaymentTerm', on_delete=models.SET_NULL, null=True)
    pricelist_id = models.IntegerField(blank=True, null=True)
    user_id = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)  # Salesperson
    team_id = models.IntegerField(blank=True, null=True)  # Sales team
    
    # Methods to add
    def action_confirm(self):
        """Confirm sales order (draft/sent → sale)"""
        if self.state in ['draft', 'sent']:
            self.state = 'sale'
            self.save()
            # Trigger stock reservation and delivery creation
            self.reserve_stock()
            self.create_delivery_orders()
            if self.invoice_policy == 'order':
                self.create_invoice()
    
    def action_cancel(self):
        """Cancel sales order"""
        self.state = 'cancel'
        self.save()
        # Unreserve stock and cancel deliveries
    
    def reserve_stock(self):
        """Reserve stock for all order lines"""
        # Integration with inventory module
        pass
    
    def create_delivery_orders(self):
        """Create delivery orders based on delivery policy"""
        # Create GoodsDeliveryNote records
        pass
```

#### **Add Stock Reservation Model:**
```python
class StockReservation(models.Model):
    """Stock reservations for sales orders"""
    sales_order = models.ForeignKey(SalesOrder, on_delete=models.CASCADE, related_name='stock_reservations')
    sales_order_line = models.ForeignKey('SalesOrderLineItem', on_delete=models.CASCADE)
    product = models.ForeignKey('Product', on_delete=models.CASCADE)
    warehouse = models.ForeignKey('inventory.Warehouse', on_delete=models.CASCADE)
    quantity_reserved = models.DecimalField(max_digits=15, decimal_places=4)
    quantity_delivered = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    state = models.CharField(max_length=20, choices=[
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('assigned', 'Assigned'),
        ('done', 'Done'),
        ('cancel', 'Cancelled'),
    ], default='draft')
    created_at = models.DateTimeField(auto_now_add=True)
```

### **2. Sales Order Line Item Updates**
```python
class SalesOrderLineItem(models.Model):
    # Add Odoo-style fields
    product_uom_qty = models.DecimalField(max_digits=15, decimal_places=4)  # Ordered quantity
    qty_delivered = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    qty_invoiced = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    qty_to_invoice = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    price_unit = models.DecimalField(max_digits=15, decimal_places=2)
    discount = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    price_subtotal = models.DecimalField(max_digits=15, decimal_places=2)
    price_total = models.DecimalField(max_digits=15, decimal_places=2)
    sequence = models.IntegerField(default=1)
```

### **3. Enhanced Goods Delivery Note Model**
```python
class GoodsDeliveryNote(models.Model):
    # Add Odoo-style fields
    name = models.CharField(max_length=100, unique=True)  # Delivery reference
    origin = models.CharField(max_length=100)  # Source document (SO number)
    scheduled_date = models.DateTimeField()
    date_done = models.DateTimeField(blank=True, null=True)
    location_id = models.ForeignKey('inventory.WarehouseLocation', on_delete=models.CASCADE)
    location_dest_id = models.ForeignKey('inventory.WarehouseLocation', on_delete=models.CASCADE, related_name='dest_deliveries')
    
    # Enhanced status
    STATE_CHOICES = [
        ('draft', 'Draft'),
        ('waiting', 'Waiting Another Operation'),
        ('confirmed', 'Waiting'),
        ('assigned', 'Ready'),
        ('done', 'Done'),
        ('cancel', 'Cancelled'),
    ]
    state = models.CharField(max_length=20, choices=STATE_CHOICES, default='draft')
    
    def action_confirm(self):
        """Confirm delivery order"""
        self.state = 'confirmed'
        self.save()
        # Check stock availability and assign if possible
    
    def action_assign(self):
        """Assign stock to delivery"""
        self.state = 'assigned'
        self.save()
    
    def action_done(self):
        """Validate delivery"""
        self.state = 'done'
        self.date_done = timezone.now()
        self.save()
        # Update inventory and sales order
        self.update_inventory()
        self.update_sales_order_delivery_status()
```

### **4. New API Endpoints** (`erp_backend/sales/views.py`)

#### **Add Odoo-style Action Methods:**
```python
class SalesOrderViewSet(viewsets.ModelViewSet):
    @action(detail=True, methods=['post'])
    def action_confirm(self, request, pk=None):
        """Confirm sales order (Odoo-style)"""
        sales_order = self.get_object()
        try:
            sales_order.action_confirm()
            return Response({'status': 'confirmed'})
        except Exception as e:
            return Response({'error': str(e)}, status=400)
    
    @action(detail=True, methods=['post'])
    def action_quotation_sent(self, request, pk=None):
        """Mark quotation as sent"""
        sales_order = self.get_object()
        sales_order.state = 'sent'
        sales_order.save()
        return Response({'status': 'sent'})
    
    @action(detail=True, methods=['get'])
    def delivery_status(self, request, pk=None):
        """Get delivery status for sales order lines"""
        sales_order = self.get_object()
        # Return delivery status for each line
        pass
    
    @action(detail=True, methods=['post'])
    def create_invoice(self, request, pk=None):
        """Create invoice from sales order"""
        sales_order = self.get_object()
        # Create invoice based on invoice_policy
        pass
```

#### **Add Stock Integration Endpoints:**
```python
@api_view(['POST'])
def reserve_stock(request):
    """Reserve stock for sales order"""
    sales_order_id = request.data.get('sales_order_id')
    # Implementation
    pass

@api_view(['POST'])
def check_availability(request):
    """Check product availability"""
    lines = request.data.get('lines')
    # Implementation
    pass

@api_view(['POST'])
def create_delivery_order(request):
    """Create delivery order from sales order"""
    # Implementation
    pass
```

### **5. Inventory Integration** (`erp_backend/inventory/models.py`)

#### **Add Stock Reservation Support:**
```python
class Inventory(models.Model):
    # Add reservation tracking
    quantity_reserved = models.DecimalField(max_digits=15, decimal_places=4, default=0)
    
    @property
    def quantity_available(self):
        """Available quantity (on hand - reserved)"""
        return self.quantity_on_hand - self.quantity_reserved
    
    def reserve_quantity(self, quantity, sales_order_line):
        """Reserve quantity for sales order"""
        if self.quantity_available >= quantity:
            self.quantity_reserved += quantity
            self.save()
            return True
        return False
```

#### **Enhanced Stock Transaction Types:**
```python
class StockTransaction(models.Model):
    # Add new transaction types
    TRANSACTION_TYPE_CHOICES = [
        # ... existing types ...
        ('RESERVATION', 'Stock Reservation'),
        ('UNRESERVATION', 'Stock Unreservation'),
        ('DELIVERY', 'Delivery Transaction'),
    ]
```

## 🎨 **Frontend Changes Required**

### **1. New Components to Create**

#### **Delivery Notes Management:**
```typescript
// src/domains/sales/components/DeliveryNotesList.tsx
// src/domains/sales/components/DeliveryNoteForm.tsx
// src/domains/sales/components/DeliveryNoteDetails.tsx
// src/domains/sales/pages/DeliveryNotesPage.tsx
```

#### **Stock Integration Components:**
```typescript
// src/shared/components/StockAvailabilityChecker.tsx
// src/shared/components/StockReservationStatus.tsx
// src/domains/inventory/components/StockMovesTable.tsx
```

### **2. Update Existing Components**

#### **Sales Order Form Updates:**
```typescript
// src/domains/sales/components/SalesOrderForm.tsx
// Add Odoo-style fields:
// - invoice_policy selector
// - delivery_policy selector  
// - state management
// - action buttons (Confirm, Send, Cancel)
```

#### **Sales Order List Updates:**
```typescript
// src/domains/sales/pages/SalesOrdersPage.tsx
// Add columns:
// - State (draft/sent/sale/done)
// - Delivery Status
// - Invoice Status
// - Action buttons per state
```

### **3. New Services Integration**

#### **Update Sales Order Service:**
```typescript
// src/services/salesOrder.service.ts
// Add Odoo-style methods:
async actionConfirm(id: number): Promise<SalesOrder>
async actionQuotationSent(id: number): Promise<SalesOrder>
async getDeliveryStatus(id: number): Promise<DeliveryStatus>
async createInvoice(id: number, options: InvoiceOptions): Promise<Invoice>
```

#### **Enhanced Delivery Note Service:**
```typescript
// src/services/goodsDeliveryNote.service.ts
// Add inventory integration:
async validateDelivery(id: number, moves: StockMove[]): Promise<DeliveryNote>
async getStockMoves(id: number): Promise<StockMove[]>
async updateStockMove(moveId: number, data: any): Promise<StockMove>
```

### **4. Navigation Updates**

#### **Add Delivery Notes to Sales Menu:**
```typescript
// src/domains/sales/config/salesMenuConfig.ts
export const salesMenuItems = [
  // ... existing items ...
  {
    title: 'Delivery Notes',
    path: '/dashboard/sales/delivery-notes',
    icon: LocalShippingIcon,
  },
];
```

#### **Add Routes:**
```typescript
// src/routes/AppRoutes.tsx
// Add delivery note routes:
<Route path="/dashboard/sales/delivery-notes" element={<DeliveryNotesPage />} />
<Route path="/dashboard/sales/delivery-notes/:id" element={<DeliveryNoteDetails />} />
<Route path="/dashboard/sales/orders/:id/deliveries" element={<OrderDeliveriesPage />} />
```

## 🔗 **Inventory Module Integration**

### **Missing Delivery Note Frontend Components**

Currently, your system has:
- ✅ Backend models for GoodsDeliveryNote
- ✅ Backend API endpoints
- ✅ Backend inventory integration
- ❌ **Missing**: Frontend delivery note components
- ❌ **Missing**: Delivery note list/detail pages
- ❌ **Missing**: Stock reservation UI
- ❌ **Missing**: Delivery validation interface

### **Required Delivery Note Components:**

1. **DeliveryNotesPage.tsx** - List all delivery notes
2. **DeliveryNoteForm.tsx** - Create/edit delivery notes  
3. **DeliveryNoteDetails.tsx** - View delivery note details
4. **StockMovesTable.tsx** - Show stock movements
5. **DeliveryValidation.tsx** - Validate deliveries with quantities

### **Inventory Integration Points:**

1. **Stock Availability Display** in sales order lines
2. **Reservation Status** showing reserved vs available stock
3. **Delivery Validation** with actual quantities delivered
4. **Stock Movement Tracking** from sales to delivery
5. **Automatic Inventory Updates** on delivery confirmation

## 📋 **Implementation Priority**

### **Phase 1: Backend Foundation**
1. Update SalesOrder model with Odoo fields
2. Add StockReservation model
3. Implement action methods (confirm, cancel, etc.)
4. Add stock integration endpoints

### **Phase 2: Frontend Core**
1. Update existing sales order components
2. Add delivery note management pages
3. Integrate stock availability checking
4. Add state-based action buttons

### **Phase 3: Advanced Features**
1. Stock reservation UI
2. Delivery validation interface
3. Automatic invoice creation
4. Advanced reporting and analytics

This implementation will give you a complete Odoo-style sales workflow with proper inventory integration!

## 📦 **Delivery Note Frontend Components Created**

### **1. DeliveryNotesPage.tsx** ✅ Created
- Complete delivery notes list with filtering and search
- Status-based statistics cards
- Action menu with validate, edit, delete options
- Integration with existing goodsDeliveryNote.service.ts

### **2. Missing Components Still Needed:**
- **DeliveryNoteDetails.tsx** - View delivery note details with stock moves
- **DeliveryNoteForm.tsx** - Create/edit delivery notes
- **StockMovesTable.tsx** - Show stock movements for delivery
- **DeliveryValidation.tsx** - Validate deliveries with actual quantities

### **3. Navigation Integration Required:**
Add to `src/domains/sales/config/salesMenuConfig.ts`:
```typescript
{
  title: 'Delivery Notes',
  path: '/dashboard/sales/delivery-notes',
  icon: LocalShippingIcon,
}
```

Add routes to `src/routes/AppRoutes.tsx`:
```typescript
<Route path="/dashboard/sales/delivery-notes" element={<DeliveryNotesPage />} />
<Route path="/dashboard/sales/delivery-notes/:id" element={<DeliveryNoteDetails />} />
```
