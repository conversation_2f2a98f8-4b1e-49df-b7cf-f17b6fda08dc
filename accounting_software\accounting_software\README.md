# Accounting Software

A comprehensive accounting and business management solution built with modern web technologies.

## Overview

This enterprise-grade application provides a complete suite of accounting and business management tools, featuring a modular architecture and robust user management system.

## Core Features

- **User Authentication & Authorization**
  - Role-based access control
  - Multi-level permissions system
  - Secure password policies
  
- **Company Management**
  - Multi-company support
  - Fiscal/Accounting year configuration
  - Tax and regulatory compliance settings
  
- **Module Integration**
  - Accounting: General ledger, journal entries
  - Sales: Invoicing, customer management, estimates
  - Purchases: Vendor management, purchase orders
  - Fixed Assets: Asset tracking and depreciation
  - Inventory: Stock management, valuations
  - HR: Employee management, payroll

## Shared Features

### Company Integration
The application implements company-related features across different modules:

1. **Global State Management**
   - `CompanyContext`: Provides company information throughout the application
   - Located in: `src/contexts/CompanyContext.tsx`
   - Used for: Multi-company support, company switching, company settings

2. **Shared Types**
   - Location: `src/shared/types/company.types.ts`
   - Key interfaces:
     - `CompanyFormData`: Company creation/editing form data
     - `Company`: Complete company model with system fields
   - Used across: Forms, API responses, data display

3. **Company Features in Modules**
   - **Sales Module**: Company-specific customer management, revenue tracking
   - **Users Module**: Company-specific user management and permissions
   - **Auth Module**: Company-based authentication and authorization

4. **Reusable Components**
   - `CompanyForm`: Standardized company data entry
   - Location: `src/domains/company/components/forms/CompanyForm.tsx`
   - Used in: Company creation, company profile editing

5. **API Integration**
   - Company-specific API endpoints
   - Company-based data filtering
   - Multi-company data separation

This shared architecture ensures:
- Consistent company data handling
- Centralized company state management
- Standardized company-related components
- Type safety across company-related features

## Technical Stack

### Frontend Framework
- **React 18**: Latest version with improved concurrent rendering
- **TypeScript**: For type safety and better developer experience
- **Vite**: Modern build tool offering faster development experience

### UI Components
- **Material-UI (v5)**: Comprehensive UI component library
- **@mui/x-date-pickers**: Advanced date/time input components
- **@mui/icons-material**: Material Design icons

### State Management & Routing
- **Redux Toolkit**: Efficient state management with simplified setup
- **React Router (v6)**: Dynamic routing with latest features

### Form Management & Validation
- **Formik**: Form state and validation management
- **Yup**: Schema-based form validation
- **dayjs**: Date manipulation library

### Data Visualization
- **Recharts**: Responsive charting library for analytics

### Development Tools
- **ESLint**: Code quality and consistency
- **TypeScript**: Static type checking

## Project Structure

```
src/
├── assets/                    # Static resources
│   └── styles/               # Global styles and theme configurations
│
├── config/                   # Application configuration
│   └── app.config.ts        # App-wide configuration constants
│
├── contexts/                 # React Context providers
│   └── CompanyContext.tsx   # Company-related context management
│
├── domains/                  # Feature-based modules
│   ├── auth/                # Authentication module
│   │   ├── components/      
│   │   │   └── LoginForm.tsx
│   │   ├── pages/
│   │   │   └── LoginPage.tsx
│   │   └── services/
│   │       └── auth.service.ts
│   │
│   ├── company/             # Company management
│   │   ├── components/
│   │   │   └── forms/
│   │   │       └── CompanyForm.tsx
│   │   └── pages/
│   │       └── CreateCompanyPage.tsx
│   │
│   ├── sales/               # Sales module (cleaned - customers only)
│   │   ├── components/
│   │   │   ├── CustomerForm.tsx
│   │   │   ├── RecentSales.tsx
│   │   │   ├── SalesChart.tsx
│   │   │   ├── SalesStats.tsx
│   │   │   └── TopCustomers.tsx
│   │   └── pages/
│   │       ├── AllSalesPage.tsx
│   │       ├── CustomersPage.tsx
│   │       └── SalesPage.tsx
│   │
│   └── users/               # User management
│       ├── components/
│       │   ├── UserList.tsx
│       │   └── forms/
│       │       └── AddUserForm.tsx
│       └── pages/
│           └── UserManagementPage.tsx
│
├── layouts/                 # Layout components
│   ├── DashboardLayout.tsx # Main application layout
│   ├── Sidebar.tsx        # Navigation sidebar
│   └── Topbar.tsx         # Top navigation bar
│
├── services/               # Common services
│   ├── api.ts             # Axios instance and interceptors
│   └── auth.service.ts    # Authentication service
│
├── shared/                # Shared components and utilities
│   ├── components/
│   │   ├── DataTable.tsx  # Reusable table component
│   │   ├── ErrorBoundary.tsx
│   │   └── FormDialog.tsx
│   ├── types/
│   │   └── common.ts      # Shared TypeScript interfaces
│   └── utils/
│       └── formatters.ts  # Common formatting functions
│
├── store/                 # Redux store configuration
│   ├── index.ts          # Store setup and configuration
│   └── slices/           # Redux toolkit slices
│
├── App.tsx               # Root application component
├── env.d.ts             # Environment variables type definitions
├── main.tsx             # Application entry point
└── theme.ts             # Material-UI theme configuration

public/                   # Public assets
├── index.html           # HTML entry point
└── vite.svg            # Application favicon

Configuration Files
├── .gitignore          # Git ignore patterns
├── package.json        # Project dependencies and scripts
├── tsconfig.json       # TypeScript configuration
├── tsconfig.node.json  # TypeScript config for Node.js
├── vite.config.ts      # Vite bundler configuration
└── eslint.config.js    # ESLint configuration
```

## Directory Structure Explanation

### `/src` Directory
- **assets/**: Contains static resources like global styles and images
- **config/**: Application-wide configuration settings
- **contexts/**: React Context providers for state management
- **domains/**: Feature-based modules following domain-driven design
  - Each domain contains its own components, pages, and services
  - Promotes separation of concerns and modularity
- **layouts/**: Layout components for consistent UI structure
- **services/**: Common services for API communication
- **shared/**: Reusable components and utilities
- **store/**: Redux store configuration and state management

### Domain Structure
Each domain (auth, company, sales, users) follows a consistent structure:
- **components/**: Reusable components specific to the domain
- **pages/**: Route components (views)
- **services/**: Domain-specific API services
- **types/**: TypeScript interfaces and types
- **utils/**: Domain-specific utility functions

### Configuration Files
- **package.json**: Project metadata and dependencies
- **tsconfig.json**: TypeScript compiler configuration
- **vite.config.ts**: Build tool configuration
- **eslint.config.js**: Code linting rules
- **.gitignore**: Version control ignore patterns

### Key Files
- **App.tsx**: Root component defining main routing and providers
- **main.tsx**: Application entry point
- **env.d.ts**: Environment variable type definitions
- **theme.ts**: Material-UI theme customization

## File Naming Conventions
- React components: PascalCase (e.g., `UserList.tsx`)
- Services and utilities: camelCase (e.g., `auth.service.ts`)
- Configuration files: lowercase with appropriate extension
- Type definitions: camelCase with `.d.ts` extension

## Module Organization
- Each module is self-contained with its own components, pages, and services
- Shared functionality is extracted to the `/shared` directory
- Common services are placed in the `/services` directory
- Layout components are separated in the `/layouts` directory

This structure promotes:
- Clear separation of concerns
- Easy module location
- Consistent organization
- Scalable architecture
- Maintainable codebase

## Getting Started

### System Requirements
- Node.js v16 or higher
- npm v7 or higher
- Modern web browser with JavaScript enabled

### Development Setup

1. Clone the repository:
```bash
git clone [repository-url]
cd accounting_software
```

2. Install dependencies:
```bash
npm install
```

3. Start development server:
```bash
npm run dev
```

The application will be available at `http://localhost:5173`

### Available Scripts

- `npm run dev`: Start development server
- `npm run build`: Create production build
- `npm run lint`: Run ESLint code analysis
- `npm run preview`: Preview production build locally

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## Code Style Guidelines

- Use TypeScript for all new components
- Follow Material-UI theming patterns
- Implement form validation using Yup schemas
- Use Redux Toolkit for state management
- Follow the domain-driven project structure

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please open an issue in the repository or contact the development team.
