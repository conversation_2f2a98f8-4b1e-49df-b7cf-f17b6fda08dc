#!/usr/bin/env python3
"""
Check what users exist in the database
"""

import os
import sys
import django

# Add the Django project to the path
sys.path.append('erp_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')

try:
    django.setup()
    from django.contrib.auth.models import User
    
    print("🔍 Checking Django Users...")
    print("=" * 30)
    
    users = User.objects.all()
    
    if users.exists():
        print(f"Found {users.count()} users:")
        for user in users:
            print(f"  - Username: {user.username}")
            print(f"    Email: {user.email}")
            print(f"    Is Active: {user.is_active}")
            print(f"    Is Staff: {user.is_staff}")
            print(f"    Is Superuser: {user.is_superuser}")
            print()
    else:
        print("❌ No users found!")
        print("Creating a default admin user...")
        
        # Create default admin user
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print(f"✅ Created admin user: admin / admin123")
        
except Exception as e:
    print(f"❌ Error: {e}")
    print("Make sure Django is properly configured")
