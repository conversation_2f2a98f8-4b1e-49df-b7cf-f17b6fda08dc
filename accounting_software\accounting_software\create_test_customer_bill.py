#!/usr/bin/env python3

import os
import sys
import django
from decimal import Decimal

# Add the erp_backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'erp_backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

def create_test_customer_bill():
    """Create a test customer bill and post it to generate GL entries"""
    
    print("🧪 Creating Test Customer Bill")
    print("=" * 40)
    
    try:
        # Import models
        from sales.models import CustomerBill, CustomerBillItem
        from contacts.models import Contact
        from gl.models import JournalEntry
        from django.contrib.auth.models import User
        
        # Get first available user and customer
        user = User.objects.first()
        customer = Contact.objects.filter(contact_type='customer').first()
        
        if not user:
            print("❌ No users found. Please create a superuser first:")
            print("   python manage.py createsuperuser")
            return False
            
        if not customer:
            print("❌ No customers found. Please create a customer first.")
            return False
            
        print(f"✅ Using user: {user.username}")
        print(f"✅ Using customer: {customer.name}")
        
        # Create a unique customer bill
        import time
        unique_id = int(time.time())
        bill_number = f'TEST-BILL-{unique_id}'
        
        print(f"\n🔨 Creating customer bill: {bill_number}")
        
        # Create customer bill
        customer_bill = CustomerBill.objects.create(
            bill_number=bill_number,
            customer=customer,
            bill_type='bill',
            bill_date='2024-07-11',
            due_date='2024-08-11',
            subtotal=Decimal('500.00'),
            tax_amount=Decimal('50.00'),
            total_amount=Decimal('550.00'),
            amount_paid=Decimal('0.00'),
            balance_due=Decimal('550.00'),
            status='draft',
            reference_number=f'TEST-REF-{unique_id}',
            notes='Test customer bill created via script',
            created_by=user
        )
        
        print(f"✅ Created customer bill: {customer_bill.bill_number}")
        print(f"   Status: {customer_bill.status}")
        print(f"   Total: ${customer_bill.total_amount}")
        print(f"   Customer: {customer_bill.customer.name}")
        
        # Check journal entries before posting
        before_count = JournalEntry.objects.count()
        print(f"\n📊 Journal entries before posting: {before_count}")
        
        # Post the bill
        print(f"\n📮 Posting customer bill...")
        customer_bill.status = 'posted'
        customer_bill.save()  # This should trigger GL entry creation
        
        print(f"✅ Posted customer bill: {customer_bill.bill_number}")
        print(f"   Status: {customer_bill.status}")
        
        # Check journal entries after posting
        after_count = JournalEntry.objects.count()
        print(f"📊 Journal entries after posting: {after_count}")
        
        if after_count > before_count:
            print(f"🎉 SUCCESS! {after_count - before_count} journal entry(ies) created!")
            
            # Show the created entries
            new_entries = JournalEntry.objects.filter(
                reference_number__icontains=customer_bill.bill_number
            )
            
            for entry in new_entries:
                print(f"\n📝 Journal Entry Created:")
                print(f"   Entry Number: {entry.entry_number}")
                print(f"   Reference: {entry.reference_number}")
                print(f"   Status: {entry.status}")
                print(f"   Date: {entry.entry_date}")
                
                lines = entry.journal_lines.all()
                print(f"   Lines: {lines.count()}")
                for line in lines:
                    if line.debit_amount > 0:
                        account_name = line.account.account_name if line.account else 'No Account'
                        print(f"     DEBIT  ${line.debit_amount} - {account_name}")
                    if line.credit_amount > 0:
                        account_name = line.account.account_name if line.account else 'No Account'
                        print(f"     CREDIT ${line.credit_amount} - {account_name}")
            
            return True
        else:
            print("❌ No journal entries were created!")
            print("   Check the create_gl_entries method in CustomerBill model")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_test_customer_bill()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 Test Customer Bill Created Successfully!")
        print("✅ Customer bill posted and GL entries created")
        print("\n💡 Now check:")
        print("1. Django Admin → Sales → Customer Bills")
        print("2. Django Admin → GL → Journal Entries")
        print("3. Frontend Customer Bills page")
        print("4. Frontend GL Journal Entries page")
    else:
        print("❌ Test failed - check the error messages above")
    
    print(f"\n🌐 Django Admin: http://localhost:8000/admin/")
    print(f"🌐 Customer Bills: http://localhost:8000/admin/sales/customerbill/")
    print(f"🌐 Journal Entries: http://localhost:8000/admin/gl/journalentry/")
