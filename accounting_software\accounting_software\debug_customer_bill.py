#!/usr/bin/env python3

import os
import sys
import django
from decimal import Decimal

# Add the erp_backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'erp_backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

def debug_customer_bill():
    """Debug customer bill creation and GL integration"""
    
    print("🔍 Debugging Customer Bill Creation")
    print("=" * 50)
    
    try:
        # Import models
        from sales.models import CustomerBill, CustomerBillItem
        from contacts.models import Contact
        from gl.models import JournalEntry, JournalEntryLine, Account
        from django.contrib.auth.models import User
        
        # Check current state
        print("📊 Current Database State:")
        print(f"   Customer Bills: {CustomerBill.objects.count()}")
        print(f"   Journal Entries: {JournalEntry.objects.count()}")
        print(f"   GL Accounts: {Account.objects.count()}")
        print(f"   Contacts: {Contact.objects.count()}")
        print(f"   Users: {User.objects.count()}")
        
        # Get first available user and customer
        user = User.objects.first()
        customer = Contact.objects.filter(contact_type='customer').first()
        
        if not user:
            print("❌ No users found!")
            return False
            
        if not customer:
            print("❌ No customers found!")
            return False
            
        print(f"\n✅ Using user: {user.username}")
        print(f"✅ Using customer: {customer.name}")
        
        # Create a test customer bill with detailed logging
        import time
        unique_id = int(time.time())
        bill_number = f'DEBUG-{unique_id}'
        
        print(f"\n🔨 Creating customer bill: {bill_number}")
        
        # Create customer bill in draft first
        customer_bill = CustomerBill(
            bill_number=bill_number,
            customer=customer,
            bill_type='bill',
            bill_date='2024-07-11',
            due_date='2024-08-11',
            subtotal=Decimal('100.00'),
            tax_amount=Decimal('10.00'),
            total_amount=Decimal('110.00'),
            amount_paid=Decimal('0.00'),
            balance_due=Decimal('110.00'),
            status='draft',
            reference_number=f'DEBUG-REF-{unique_id}',
            notes='Debug test customer bill',
            created_by=user
        )
        
        print(f"✅ Customer bill object created (not saved yet)")
        print(f"   Status: {customer_bill.status}")
        print(f"   Total: ${customer_bill.total_amount}")
        
        # Save as draft first
        customer_bill.save()
        print(f"✅ Customer bill saved as draft: {customer_bill.id}")
        
        # Check journal entries before posting
        before_count = JournalEntry.objects.count()
        print(f"📊 Journal entries before posting: {before_count}")
        
        # Now post the bill
        print(f"\n📮 Posting customer bill...")
        customer_bill.status = 'posted'
        
        try:
            customer_bill.save()
            print(f"✅ Customer bill save() completed")
        except Exception as e:
            print(f"❌ Error during save(): {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Check journal entries after posting
        after_count = JournalEntry.objects.count()
        print(f"📊 Journal entries after posting: {after_count}")
        
        if after_count > before_count:
            print(f"🎉 SUCCESS! {after_count - before_count} journal entry(ies) created!")
            
            # Find the created entry
            new_entry = JournalEntry.objects.filter(
                reference_number=f"CB-{customer_bill.bill_number}"
            ).first()
            
            if new_entry:
                print(f"\n📝 Journal Entry Details:")
                print(f"   Entry Number: {new_entry.entry_number}")
                print(f"   Reference: {new_entry.reference_number}")
                print(f"   Status: {new_entry.status}")
                print(f"   Description: {new_entry.description}")
                
                lines = new_entry.journal_lines.all()
                print(f"   Lines: {lines.count()}")
                
                for line in lines:
                    account_name = line.account.account_name if line.account else 'No Account'
                    if line.debit_amount > 0:
                        print(f"     DEBIT  ${line.debit_amount} - {account_name}")
                    if line.credit_amount > 0:
                        print(f"     CREDIT ${line.credit_amount} - {account_name}")
                        
                if lines.count() == 0:
                    print(f"   ⚠️ No journal lines found - this indicates an issue")
                    
                    # Check if accounts exist
                    print(f"\n🔍 Checking GL Accounts:")
                    ar_accounts = Account.objects.filter(account_name__icontains='Receivable')
                    revenue_accounts = Account.objects.filter(account_name__icontains='Revenue')
                    tax_accounts = Account.objects.filter(account_name__icontains='Tax')
                    
                    print(f"   Accounts Receivable accounts: {ar_accounts.count()}")
                    for acc in ar_accounts:
                        print(f"     - {acc.account_number} {acc.account_name}")
                        
                    print(f"   Revenue accounts: {revenue_accounts.count()}")
                    for acc in revenue_accounts:
                        print(f"     - {acc.account_number} {acc.account_name}")
                        
                    print(f"   Tax accounts: {tax_accounts.count()}")
                    for acc in tax_accounts:
                        print(f"     - {acc.account_number} {acc.account_name}")
            
            return True
        else:
            print("❌ No journal entries were created!")
            print("   This indicates the create_gl_entries method is not working")
            
            # Try to manually call create_gl_entries
            print(f"\n🔧 Manually testing create_gl_entries...")
            try:
                result = customer_bill.create_gl_entries()
                print(f"   Manual GL creation result: {result}")
            except Exception as e:
                print(f"   ❌ Error in create_gl_entries: {e}")
                import traceback
                traceback.print_exc()
            
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_customer_bill()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Customer Bill Creation and GL Integration Working!")
    else:
        print("❌ Issues found - check the debug output above")
    
    print(f"\n🌐 Check Django Admin:")
    print(f"- Customer Bills: http://localhost:8000/admin/sales/customerbill/")
    print(f"- Journal Entries: http://localhost:8000/admin/gl/journalentry/")
