# Generated by Django 4.2.21 on 2025-07-14 00:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contacts', '0006_remove_pricingrule_customer_and_more'),
        ('Pricing', '0002_alter_pricelistitem_product'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customerpricelist',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contacts.contact'),
        ),
        migrations.AlterField(
            model_name='pricelistitem',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Pricing.product'),
        ),
    ]
