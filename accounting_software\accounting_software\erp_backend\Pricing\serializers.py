from rest_framework import serializers
from .models import PriceList, PriceListItem, DiscountRule, Product

class ProductSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    tax_rate = serializers.DecimalField(source='tax_code.rate', max_digits=5, decimal_places=2, read_only=True)
    
    class Meta:
        model = Product
        fields = ['id', 'code', 'name', 'description', 'type', 'category', 'category_name',
                 'uom', 'cost_method', 'standard_cost', 'average_cost', 'last_cost',
                 'tax_code', 'tax_rate', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['id', 'average_cost', 'last_cost', 'created_at', 'updated_at']

class PriceListSerializer(serializers.ModelSerializer):
    class Meta:
        model = PriceList
        fields = ['id', 'name', 'description', 'currency', 'valid_from', 
                 'valid_to', 'is_active', 'is_default']
        read_only_fields = ['id']

class PriceListItemSerializer(serializers.ModelSerializer):
    product_code = serializers.CharField(source='product.code', read_only=True)
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_type = serializers.CharField(source='product.type', read_only=True)

    class Meta:
        model = PriceListItem
        fields = ['id', 'price_list', 'product', 'product_code', 'product_name',
                 'product_type', 'unit_price', 'min_quantity', 'discount_percent',
                 'effective_date', 'expiry_date']
        read_only_fields = ['id']

class DiscountRuleSerializer(serializers.ModelSerializer):
    class Meta:
        model = DiscountRule
        fields = ['id', 'name', 'description', 'discount_percent', 'min_quantity',
                 'customer_group', 'product_category', 'start_date', 'end_date',
                 'is_active']
        read_only_fields = ['id']