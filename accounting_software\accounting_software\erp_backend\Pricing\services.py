from django.utils import timezone
from django.db import models
from decimal import Decimal
from .models import PriceList, PriceListItem, DiscountRule, CustomerPriceList
from sales.models import Product as SalesProduct

class PricingService:
    @staticmethod
    def get_customer_price_list(customer):
        """
        Get the active price list for a customer
        Falls back to default price list if no specific list is assigned
        """
        today = timezone.now().date()
        
        # First try to get specific price list for customer
        customer_price = CustomerPriceList.objects.filter(
            customer=customer,
            effective_date__lte=today,
            expiry_date__gte=today
        ).first()
        
        if customer_price:
            return customer_price.price_list
        
        # Fall back to default price list
        return PriceList.objects.filter(is_default=True, is_active=True).first()
    
    @staticmethod
    def get_product_price(product, customer=None, quantity=1, date=None):
        """
        Get the price for a product considering:
        - Customer-specific price lists
        - Quantity breaks
        - Active discounts
        """
        if date is None:
            date = timezone.now().date()
        
        quantity = Decimal(str(quantity))
        base_price = None
        final_price = None
        
        # Step 1: Get base price from price list
        if customer:
            price_list = PricingService.get_customer_price_list(customer)
        else:
            price_list = PriceList.objects.filter(is_default=True, is_active=True).first()
        
        if price_list:
            # Find the best price based on quantity break
            price_item = PriceListItem.objects.filter(
                price_list=price_list,
                product=product,
                min_quantity__lte=quantity,
                effective_date__lte=date
            ).filter(
                # Include items with no expiry date or expiry date in the future
                models.Q(expiry_date__isnull=True) | models.Q(expiry_date__gte=date)
            ).order_by('-min_quantity').first()
            
            if price_item:
                base_price = price_item.unit_price
                # Apply price list discount if any
                if price_item.discount_percent > 0:
                    base_price = base_price * (1 - price_item.discount_percent / 100)
        
        # If no price list item found, use product's standard price (if exists)
        if base_price is None:
            # In a real system, you might have a 'standard_price' field on Product
            # For now, we'll return None if no price is found
            return None
        
        final_price = base_price
        
        # Step 2: Apply any additional discount rules
        discount_rules = DiscountRule.objects.filter(
            is_active=True,
            start_date__lte=date,
            end_date__gte=date
        )
        
        # Apply customer group discounts
        if customer and hasattr(customer, 'customer_group'):
            group_discounts = discount_rules.filter(
                customer_group=customer.customer_group,
                min_quantity__lte=quantity
            ).order_by('-discount_percent')

            if group_discounts.exists():
                best_discount = group_discounts.first()
                final_price = final_price * (1 - best_discount.discount_percent / 100)
        
        # Apply category discounts
        if product.category:
            category_discounts = discount_rules.filter(
                product_category=product.category,
                min_quantity__lte=quantity
            ).order_by('-discount_percent')
            
            if category_discounts.exists():
                best_discount = category_discounts.first()
                final_price = final_price * (1 - best_discount.discount_percent / 100)
        
        return final_price.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_line_total(product, unit_price, quantity, tax_inclusive=False):
        """
        Calculate line total with tax
        """
        quantity = Decimal(str(quantity))
        unit_price = Decimal(str(unit_price))
        
        subtotal = unit_price * quantity
        
        if not product.tax_code:
            return {
                'subtotal': subtotal,
                'tax_amount': Decimal('0'),
                'total': subtotal
            }
        
        tax_rate = product.tax_code.rate / 100
        
        if tax_inclusive:
            # Price already includes tax
            tax_amount = subtotal - (subtotal / (1 + tax_rate))
            return {
                'subtotal': subtotal - tax_amount,
                'tax_amount': tax_amount,
                'total': subtotal
            }
        else:
            # Add tax to price
            tax_amount = subtotal * tax_rate
            return {
                'subtotal': subtotal,
                'tax_amount': tax_amount,
                'total': subtotal + tax_amount
            }