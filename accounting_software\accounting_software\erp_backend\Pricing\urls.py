from django.urls import path, include
from rest_framework.routers import <PERSON>faultRouter
from .views import (
    ProductViewSet, PriceListViewSet, PriceListItemViewSet,
    DiscountRuleViewSet, ProductCostViewSet, PricingViewSet
)

router = DefaultRouter()
router.register(r'products', ProductViewSet)
router.register(r'price-lists', PriceListViewSet)
router.register(r'price-list-items', PriceListItemViewSet)
router.register(r'discount-rules', DiscountRuleViewSet)
router.register(r'product-costs', ProductCostViewSet)
router.register(r'pricing', PricingViewSet, basename='pricing')

urlpatterns = [
    path('', include(router.urls)),
]