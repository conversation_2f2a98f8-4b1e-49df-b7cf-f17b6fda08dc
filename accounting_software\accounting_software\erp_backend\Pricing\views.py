from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db.models import F, Avg
from .models import PriceList, PriceListItem, DiscountRule, Product
from .serializers import PriceListSerializer, PriceListItemSerializer, DiscountRuleSerializer, ProductSerializer
from .services import PricingService
from contacts.models import Contact

class ProductViewSet(viewsets.ModelViewSet):
    """ViewSet for Product model"""
    queryset = Product.objects.filter(is_active=True)
    serializer_class = ProductSerializer
    
    def get_queryset(self):
        """Optionally filter by type or category"""
        queryset = super().get_queryset()
        product_type = self.request.query_params.get('type')
        category = self.request.query_params.get('category')
        
        if product_type:
            queryset = queryset.filter(type=product_type)
        if category:
            queryset = queryset.filter(category=category)
        
        return queryset

class PriceListViewSet(viewsets.ModelViewSet):
    queryset = PriceList.objects.all()
    serializer_class = PriceListSerializer
    
    @action(detail=True, methods=['get'])
    def items(self, request, pk=None):
        """Get all items for a specific price list"""
        price_list = self.get_object()
        items = PriceListItem.objects.filter(price_list=price_list)
        serializer = PriceListItemSerializer(items, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def default(self, request):
        """Get the default price list"""
        price_list = PriceList.objects.filter(is_default=True, is_active=True).first()
        if not price_list:
            return Response({'detail': 'No default price list found'}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = self.get_serializer(price_list)
        return Response(serializer.data)

class PriceListItemViewSet(viewsets.ModelViewSet):
    queryset = PriceListItem.objects.all()
    serializer_class = PriceListItemSerializer
    
    def get_queryset(self):
        """Optionally filter by price_list and/or product"""
        queryset = super().get_queryset()
        price_list = self.request.query_params.get('price_list')
        product = self.request.query_params.get('product')
        
        if price_list:
            queryset = queryset.filter(price_list=price_list)
        if product:
            queryset = queryset.filter(product=product)
        
        return queryset

class DiscountRuleViewSet(viewsets.ModelViewSet):
    queryset = DiscountRule.objects.all()
    serializer_class = DiscountRuleSerializer
    
    def get_queryset(self):
        """Optionally filter by active status"""
        queryset = super().get_queryset()
        is_active = self.request.query_params.get('is_active')
        
        if is_active:
            is_active = is_active.lower() in ('true', '1', 't')
            queryset = queryset.filter(is_active=is_active)
        
        return queryset

class PricingViewSet(viewsets.ViewSet):
    """Special pricing endpoints"""
    
    @action(detail=False, methods=['get'])
    def get_price(self, request):
        """Get price for a product considering all rules"""
        product_id = request.query_params.get('product')
        customer_id = request.query_params.get('customer')
        quantity = request.query_params.get('quantity', 1)
        date = request.query_params.get('date')
        
        if not product_id:
            return Response({'error': 'product parameter is required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        try:
            product = Product.objects.get(pk=product_id)
        except Product.DoesNotExist:
            return Response({'error': 'Product not found'}, 
                          status=status.HTTP_404_NOT_FOUND)
        
        customer = None
        if customer_id:
            try:
                customer = Contact.objects.get(pk=customer_id, contact_type='customer')
            except Contact.DoesNotExist:
                pass  # Proceed without customer-specific pricing
        
        try:
            quantity = float(quantity)
            if quantity <= 0:
                raise ValueError
        except ValueError:
            return Response({'error': 'quantity must be a positive number'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        price = PricingService.get_product_price(
            product=product,
            customer=customer,
            quantity=quantity,
            date=date
        )
        
        if price is None:
            return Response({'error': 'No price found for this product'},
                          status=status.HTTP_404_NOT_FOUND)

        # Get cost price based on product's cost method
        cost_price = 0
        if product.cost_method == 'standard':
            cost_price = product.standard_cost
        elif product.cost_method == 'average':
            cost_price = product.average_cost
        elif product.cost_method == 'fifo':
            cost_price = product.last_cost
        else:
            cost_price = product.standard_cost

        # Calculate margin
        margin = price - cost_price
        margin_percent = (margin / price * 100) if price > 0 else 0

        return Response({
            'product_id': product.id,
            'product_code': product.code,
            'product_name': product.name,
            'customer_id': customer.id if customer else None,
            'quantity': quantity,
            'unit_price': price,
            'cost_price': float(cost_price),
            'margin': float(margin),
            'margin_percent': float(margin_percent),
            'currency': 'USD'  # In real system, get from price list
        })

class ProductCostViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing product costs
    """
    queryset = Product.objects.all()
    serializer_class = ProductSerializer

    def get_queryset(self):
        """Get active products with cost information"""
        return self.queryset.filter(is_active=True).select_related('category', 'tax_code')

    @action(detail=False, methods=['get'])
    def cost_analysis(self, request):
        """Get cost analysis statistics"""
        queryset = self.get_queryset()
        
        # Calculate cost statistics
        stats = {
            'total_products': queryset.count(),
            'avg_standard_cost': queryset.aggregate(avg=Avg('standard_cost'))['avg'] or 0,
            'avg_last_cost': queryset.aggregate(avg=Avg('last_cost'))['avg'] or 0,
            'cost_methods': {
                'standard': queryset.filter(cost_method='standard').count(),
                'average': queryset.filter(cost_method='average').count(),
                'fifo': queryset.filter(cost_method='fifo').count(),
            }
        }
        
        return Response(stats)

    @action(detail=True, methods=['patch'])
    def update_cost(self, request, pk=None):
        """Update product cost information"""
        product = self.get_object()
        
        # Only allow updating cost_method and standard_cost
        allowed_fields = ['cost_method', 'standard_cost']
        update_data = {
            k: v for k, v in request.data.items() 
            if k in allowed_fields
        }
        
        serializer = self.get_serializer(
            product,
            data=update_data,
            partial=True
        )
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)