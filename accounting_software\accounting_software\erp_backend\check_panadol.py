#!/usr/bin/env python
"""
Check the problematic panadol product
"""
import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from sales.models import Product

def check_panadol():
    print("=== CHECKING PANADOL PRODUCT ===")
    
    try:
        product = Product.objects.get(id=54)
        print(f'Product: {product.name}')
        print(f'SKU: {product.sku}')
        print(f'Cost Price: {repr(product.cost_price)} (type: {type(product.cost_price)})')
        print(f'Unit Price: {repr(product.unit_price)} (type: {type(product.unit_price)})')
        print(f'Status: {product.status}')
        print(f'Product Type: {product.product_type}')
        
        # Try to convert cost_price to decimal
        from decimal import Decimal
        try:
            decimal_cost = Decimal(str(product.cost_price))
            print(f'✅ Cost price converts to decimal: {decimal_cost}')
        except Exception as e:
            print(f'❌ Cost price conversion error: {e}')
            
    except Product.DoesNotExist:
        print('❌ Product with ID 54 not found')

if __name__ == '__main__':
    check_panadol()
