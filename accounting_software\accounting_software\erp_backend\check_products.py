#!/usr/bin/env python
"""
Check all products and their status
"""
import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from sales.models import Product

def check_products():
    print("=== PRODUCT STATUS BREAKDOWN ===")
    total = Product.objects.count()
    active = Product.objects.filter(status='active').count()
    inactive = Product.objects.filter(status='inactive').count()
    
    print(f'Total products: {total}')
    print(f'Active products: {active}')
    print(f'Inactive products: {inactive}')
    
    print('\n=== ALL PRODUCTS ===')
    for product in Product.objects.all():
        print(f'ID: {product.id} | SKU: {product.sku} | Name: {product.name[:30]}... | Status: {product.status}')

if __name__ == '__main__':
    check_products()
