from django.core.management.base import BaseCommand
from django.db import transaction, connection
from django.apps import apps
import sys


class Command(BaseCommand):
    help = 'Clean up old Customer and Vendor models after migrating to contacts-based structure'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force cleanup even if there are dependencies',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.force = options['force']
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('=== DRY RUN MODE - NO CHANGES WILL BE MADE ==='))
        else:
            self.stdout.write(self.style.SUCCESS('=== STARTING OLD MODEL CLEANUP ==='))

        try:
            # Get model references
            SalesCustomer = apps.get_model('sales', 'Customer')
            PurchaseVendor = apps.get_model('purchase', 'Vendor')
            ContactCustomer = apps.get_model('contacts', 'Customer')
            ContactVendor = apps.get_model('contacts', 'Vendor')
            Contact = apps.get_model('contacts', 'Contact')
            PurchaseOrder = apps.get_model('purchase', 'PurchaseOrder')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error getting model references: {e}'))
            return

        with transaction.atomic():
            # Step 1: Update Purchase Orders to use contacts.Vendor
            self.stdout.write(self.style.WARNING('\n1. UPDATING PURCHASE ORDER REFERENCES'))
            
            purchase_orders_to_update = PurchaseOrder.objects.filter(vendor__isnull=False)
            self.stdout.write(f'   Found {purchase_orders_to_update.count()} purchase orders to update')
            
            updated_pos = 0
            for po in purchase_orders_to_update:
                old_vendor = po.vendor
                
                # Find matching vendor in contacts by display_name and email
                try:
                    contact_vendor = ContactVendor.objects.select_related('contact').get(
                        contact__name=old_vendor.display_name,
                        contact__email=old_vendor.email
                    )
                    
                    if not self.dry_run:
                        # This would require updating the foreign key field
                        # We'll handle this in the Django migration instead
                        pass
                        
                    self.stdout.write(f'   ✅ PO {po.po_number}: {old_vendor.display_name} -> Contact Vendor {contact_vendor.contact.name}')
                    updated_pos += 1
                    
                except ContactVendor.DoesNotExist:
                    self.stdout.write(f'   ❌ PO {po.po_number}: Could not find matching contact vendor for {old_vendor.display_name}')
            
            self.stdout.write(f'   Total POs that can be updated: {updated_pos}')
            
            # Step 2: Check remaining dependencies
            self.stdout.write(self.style.WARNING('\n2. CHECKING REMAINING DEPENDENCIES'))
            
            # Check sales customer dependencies
            sales_invoices = apps.get_model('sales', 'Invoice').objects.filter(customer__isnull=False).count()
            sales_orders = apps.get_model('sales', 'SalesOrder').objects.filter(customer__isnull=False).count()
            sales_payments = apps.get_model('sales', 'Payment').objects.filter(customer__isnull=False).count()
            sales_estimates = apps.get_model('sales', 'Estimate').objects.filter(customer__isnull=False).count()
            sales_delivery_notes = apps.get_model('sales', 'DeliveryNote').objects.filter(customer__isnull=False).count()
            
            self.stdout.write(f'   Sales dependencies: {sales_invoices} invoices, {sales_orders} orders, {sales_payments} payments, {sales_estimates} estimates, {sales_delivery_notes} delivery notes')
            
            # Check purchase vendor dependencies
            purchase_orders = PurchaseOrder.objects.filter(vendor__isnull=False).count()

            self.stdout.write(f'   Purchase dependencies: {purchase_orders} purchase orders')
            
            # Step 3: Provide cleanup recommendations
            self.stdout.write(self.style.SUCCESS('\n3. CLEANUP PLAN'))
            
            if sales_invoices == 0 and sales_orders == 0 and sales_payments == 0 and sales_estimates == 0 and sales_delivery_notes == 0:
                self.stdout.write('   ✅ Sales Customer model can be safely removed')
                safe_to_remove_customer = True
            else:
                self.stdout.write('   ⚠️  Sales Customer model has active dependencies')
                safe_to_remove_customer = False
                
            if purchase_orders <= updated_pos and vendor_payments == 0:
                self.stdout.write('   ✅ Purchase Vendor model can be safely removed after PO migration')
                safe_to_remove_vendor = True
            else:
                self.stdout.write('   ⚠️  Purchase Vendor model has active dependencies')
                safe_to_remove_vendor = False
            
            # Step 4: Show migration commands needed
            self.stdout.write(self.style.SUCCESS('\n4. DJANGO MIGRATIONS NEEDED'))
            
            if safe_to_remove_customer or self.force:
                self.stdout.write('   📝 Sales app migration to remove Customer model:')
                self.stdout.write('      python manage.py makemigrations sales --empty --name remove_old_customer_model')
                
            if safe_to_remove_vendor or self.force:
                self.stdout.write('   📝 Purchase app migration to update PO references and remove Vendor model:')
                self.stdout.write('      python manage.py makemigrations purchase --empty --name update_vendor_references')
                
            # Step 5: Show data migration status
            self.stdout.write(self.style.SUCCESS('\n5. DATA MIGRATION STATUS'))
            
            sales_customer_count = SalesCustomer.objects.count()
            purchase_vendor_count = PurchaseVendor.objects.count()
            contacts_customer_count = ContactCustomer.objects.count()
            contacts_vendor_count = ContactVendor.objects.count()
            
            self.stdout.write(f'   📊 Sales customers (old): {sales_customer_count}')
            self.stdout.write(f'   📊 Contacts customers (new): {contacts_customer_count}')
            self.stdout.write(f'   📊 Purchase vendors (old): {purchase_vendor_count}')
            self.stdout.write(f'   📊 Contacts vendors (new): {contacts_vendor_count}')
            
            if contacts_customer_count >= sales_customer_count:
                self.stdout.write('   ✅ Customer data migration appears complete')
            else:
                self.stdout.write('   ⚠️  Customer data migration may be incomplete')
                
            if contacts_vendor_count >= purchase_vendor_count:
                self.stdout.write('   ✅ Vendor data migration appears complete')
            else:
                self.stdout.write('   ⚠️  Vendor data migration may be incomplete')

        if not self.dry_run:
            self.stdout.write(self.style.SUCCESS('\n=== CLEANUP ANALYSIS COMPLETE ==='))
            self.stdout.write('Next steps:')
            self.stdout.write('1. Create and run Django migrations to update foreign key references')
            self.stdout.write('2. Remove old model definitions from models.py files')
            self.stdout.write('3. Update imports and references in code')
        else:
            self.stdout.write(self.style.WARNING('\n=== DRY RUN COMPLETE - NO CHANGES MADE ===')) 