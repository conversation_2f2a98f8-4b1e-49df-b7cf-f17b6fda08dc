#!/usr/bin/env python
"""
Create test data for sales orders and delivery notes
"""
import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from sales.models import SalesOrder, SalesOrderLineItem
from contacts.models import Contact
from Pricing.models import Product, PriceList, PriceListItem
from datetime import date

def create_test_products():
    """Create test products and price list"""
    print("Creating test products...")

    products_data = [
        {'name': 'Laptop Computer', 'code': 'LAP001', 'standard_cost': 800.00},
        {'name': 'Wireless Mouse', 'code': 'MOU001', 'standard_cost': 15.00},
        {'name': 'USB Cable', 'code': 'CAB001', 'standard_cost': 5.00},
    ]

    # Create products
    created_products = []
    for data in products_data:
        product, created = Product.objects.get_or_create(
            code=data['code'],
            defaults=data
        )
        if created:
            print(f'✅ Created product: {product.name} ({product.code})')
        else:
            print(f'📦 Product exists: {product.name} ({product.code})')
        created_products.append(product)

    # Create default price list
    price_list, created = PriceList.objects.get_or_create(
        name='Default Price List',
        defaults={
            'description': 'Default pricing for all products',
            'is_default': True,
            'is_active': True
        }
    )
    if created:
        print(f'✅ Created price list: {price_list.name}')
    else:
        print(f'💰 Price list exists: {price_list.name}')

    # Create price list items with proper pricing
    pricing_data = [
        ('LAP001', 1200.00),
        ('MOU001', 25.00),
        ('CAB001', 10.00),
    ]

    for product_code, unit_price in pricing_data:
        product = next((p for p in created_products if p.code == product_code), None)
        if product:
            price_item, created = PriceListItem.objects.get_or_create(
                price_list=price_list,
                product=product,
                defaults={
                    'unit_price': unit_price,
                    'min_quantity': 1
                }
            )
        if created:
            print(f'  💵 Added price: {product.name} @ ${price_item.unit_price}')
        else:
            print(f'  💰 Price exists: {product.name} @ ${price_item.unit_price}')

def create_test_sales_order():
    """Create a test sales order with line items"""
    print("\nCreating test sales order...")
    
    # Get a customer
    customer = Contact.objects.filter(contact_type='customer').first()
    if not customer:
        print("❌ No customers found")
        return None
        
    # Create sales order
    so = SalesOrder.objects.create(
        customer=customer,
        so_date=date.today(),
        expected_date=date.today(),
        status='acknowledged',  # This status will show in dropdown
        total_amount=0.00,  # Will be calculated
        balance_due=0.00
    )
    print(f'✅ Created sales order: {so.so_number}')
    
    # Add line items
    products = Product.objects.all()[:3]
    price_list = PriceList.objects.filter(is_default=True).first()
    total = 0

    for i, product in enumerate(products, 1):
        quantity = i * 2  # 2, 4, 6

        # Get price from price list or use standard cost
        price_item = PriceListItem.objects.filter(price_list=price_list, product=product).first()
        unit_price = price_item.unit_price if price_item else product.standard_cost
        line_total = quantity * unit_price
        
        line_item = SalesOrderLineItem.objects.create(
            sales_order=so,
            product=product,
            description=f'{product.name} - Line {i}',
            quantity=quantity,
            unit_price=unit_price,
            line_total=line_total,
            line_order=i
        )
        total += line_total
        print(f'  ➕ Added: {product.name} x {quantity} @ ${unit_price} = ${line_total}')
    
    # Update sales order total
    so.total_amount = total
    so.balance_due = total
    so.save()
    print(f'💰 Updated SO total: ${total}')
    
    return so

if __name__ == '__main__':
    print("=== Creating Test Data for Delivery Notes ===")
    
    create_test_products()
    so = create_test_sales_order()
    
    if so:
        print(f"\n🎉 Test data created successfully!")
        print(f"📋 Sales Order: {so.so_number} (Status: {so.status})")
        print(f"👤 Customer: {so.customer.name}")
        print(f"📦 Line Items: {so.line_items.count()}")
        print(f"💵 Total: ${so.total_amount}")
        print(f"\n✅ You can now create delivery notes from this sales order!")
    else:
        print("❌ Failed to create test data")
