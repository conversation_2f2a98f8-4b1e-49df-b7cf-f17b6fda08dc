#!/usr/bin/env python
"""
Debug products to find issues
"""
import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from sales.models import Product
from Pricing.serializers import SalesProductSerializer

def debug_products():
    print("=== DEBUGGING PRODUCTS ===")
    
    products = Product.objects.all()
    print(f'Total products: {products.count()}')
    
    # Check for products with missing fields
    problematic_products = []
    
    for product in products:
        try:
            # Test serialization of each product individually
            serializer = SalesProductSerializer(product)
            data = serializer.data  # This will trigger serialization
        except Exception as e:
            problematic_products.append((product, str(e)))
            print(f'❌ Product {product.id} ({product.name}): {e}')
    
    if not problematic_products:
        print('✅ All products serialize successfully!')
        print(f'Total products that can be serialized: {products.count()}')
    else:
        print(f'❌ Found {len(problematic_products)} problematic products')

if __name__ == '__main__':
    debug_products()
