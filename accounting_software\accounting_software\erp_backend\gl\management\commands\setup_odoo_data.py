"""
Django management command to set up initial Odoo-style data
Creates default sequences, journals, and other required data
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from gl.models import IrSequence, AccountJournal, Account
from account.models import Company


class Command(BaseCommand):
    help = 'Set up initial Odoo-style data (sequences, journals, etc.)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing data before creating new data',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up Odoo-style initial data...'))

        try:
            with transaction.atomic():
                if options['reset']:
                    self.reset_data()
                
                self.create_sequences()
                self.create_journals()
                
            self.stdout.write(self.style.SUCCESS('✅ Odoo-style data setup completed successfully!'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error setting up data: {str(e)}'))
            raise

    def reset_data(self):
        """Reset existing Odoo-style data"""
        self.stdout.write('🔄 Resetting existing data...')
        
        IrSequence.objects.all().delete()
        AccountJournal.objects.all().delete()
        
        self.stdout.write('✅ Data reset completed')

    def create_sequences(self):
        """Create default sequences for document numbering"""
        self.stdout.write('📝 Creating sequences...')
        
        sequences = [
            {
                'name': 'Account Move',
                'code': 'account.move',
                'prefix': 'MOVE/%(year)s/',
                'padding': 4,
                'use_date_range': True,
            },
            {
                'name': 'Customer Bills',
                'code': 'customer.bill',
                'prefix': 'BILL/%(year)s/',
                'padding': 4,
                'use_date_range': True,
            },
            {
                'name': 'Vendor Bills',
                'code': 'vendor.bill',
                'prefix': 'VBILL/%(year)s/',
                'padding': 4,
                'use_date_range': True,
            },
            {
                'name': 'Journal Entries',
                'code': 'journal.entry',
                'prefix': 'JE/%(year)s/',
                'padding': 4,
                'use_date_range': True,
            },
            {
                'name': 'Customer Invoices',
                'code': 'customer.invoice',
                'prefix': 'INV/%(year)s/',
                'padding': 4,
                'use_date_range': True,
            },
            {
                'name': 'Customer Credit Notes',
                'code': 'customer.credit',
                'prefix': 'RCRED/%(year)s/',
                'padding': 4,
                'use_date_range': True,
            },
            {
                'name': 'Vendor Credit Notes',
                'code': 'vendor.credit',
                'prefix': 'VCRED/%(year)s/',
                'padding': 4,
                'use_date_range': True,
            },
        ]

        for seq_data in sequences:
            sequence, created = IrSequence.objects.get_or_create(
                code=seq_data['code'],
                defaults=seq_data
            )
            
            if created:
                self.stdout.write(f'  ✅ Created sequence: {sequence.name}')
            else:
                self.stdout.write(f'  ℹ️  Sequence already exists: {sequence.name}')

    def create_journals(self):
        """Create default journals for different transaction types"""
        self.stdout.write('📚 Creating journals...')
        
        # Get default accounts for journals
        try:
            # Try to find appropriate accounts
            revenue_account = Account.objects.filter(
                account_type__type='REVENUE'
            ).first()
            
            expense_account = Account.objects.filter(
                account_type__type='EXPENSE'
            ).first()
            
            cash_account = Account.objects.filter(
                account_type__type='ASSET',
                account_name__icontains='cash'
            ).first()
            
            bank_account = Account.objects.filter(
                account_type__type='ASSET',
                account_name__icontains='bank'
            ).first()
            
            # Fallback to any asset account if specific accounts not found
            if not cash_account:
                cash_account = Account.objects.filter(account_type__type='ASSET').first()
            if not bank_account:
                bank_account = Account.objects.filter(account_type__type='ASSET').first()
            if not revenue_account:
                revenue_account = Account.objects.filter(account_type__type='REVENUE').first()
            if not expense_account:
                expense_account = Account.objects.filter(account_type__type='EXPENSE').first()
                
        except Exception as e:
            self.stdout.write(self.style.WARNING(f'⚠️  Could not find default accounts: {e}'))
            # Create journals without default accounts - they can be set later
            revenue_account = expense_account = cash_account = bank_account = None

        journals = [
            {
                'name': 'Sales Journal',
                'code': 'SAL',
                'type': 'sale',
                'default_account_id': revenue_account,
                'sequence_code': 'customer.invoice',
            },
            {
                'name': 'Purchase Journal',
                'code': 'PUR',
                'type': 'purchase',
                'default_account_id': expense_account,
                'sequence_code': 'vendor.bill',
            },
            {
                'name': 'Cash Journal',
                'code': 'CSH',
                'type': 'cash',
                'default_account_id': cash_account,
                'sequence_code': 'account.move',
            },
            {
                'name': 'Bank Journal',
                'code': 'BNK',
                'type': 'bank',
                'default_account_id': bank_account,
                'sequence_code': 'account.move',
            },
            {
                'name': 'Miscellaneous Journal',
                'code': 'MISC',
                'type': 'general',
                'default_account_id': None,  # No default for miscellaneous
                'sequence_code': 'journal.entry',
            },
        ]

        for journal_data in journals:
            # Get the sequence for this journal
            sequence = None
            try:
                sequence = IrSequence.objects.get(code=journal_data.pop('sequence_code'))
            except IrSequence.DoesNotExist:
                pass

            # Skip if no default account and it's required
            if journal_data['type'] in ['sale', 'purchase', 'cash', 'bank'] and not journal_data['default_account_id']:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  Skipping {journal_data["name"]} - no default account found')
                )
                continue

            journal_data['sequence_id'] = sequence
            
            journal, created = AccountJournal.objects.get_or_create(
                code=journal_data['code'],
                defaults=journal_data
            )
            
            if created:
                self.stdout.write(f'  ✅ Created journal: {journal.name} ({journal.code})')
            else:
                self.stdout.write(f'  ℹ️  Journal already exists: {journal.name} ({journal.code})')

    def display_summary(self):
        """Display summary of created data"""
        self.stdout.write('\n📊 Summary:')
        self.stdout.write(f'  Sequences: {IrSequence.objects.count()}')
        self.stdout.write(f'  Journals: {AccountJournal.objects.count()}')
        
        self.stdout.write('\n🎯 Next Steps:')
        self.stdout.write('  1. Verify journal default accounts are correct')
        self.stdout.write('  2. Test creating account moves through the frontend')
        self.stdout.write('  3. Check sequence numbering is working properly')
