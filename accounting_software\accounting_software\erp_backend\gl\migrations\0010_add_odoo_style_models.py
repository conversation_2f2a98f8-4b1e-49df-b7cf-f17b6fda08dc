# Generated by Django 4.2.21 on 2025-07-13 16:44

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0003_company_functional_currency_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sales', '0014_customerbill_delivery_note_and_more'),
        ('sales_tax', '0001_initial'),
        ('contacts', '0006_remove_pricingrule_customer_and_more'),
        ('gl', '0009_recurringjournalentry_recurringjournalentryline'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountFullReconcile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Reconciliation reference', max_length=100)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('company_id', models.ForeignKey(blank=True, help_text='Company this reconciliation belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, to='account.company')),
            ],
            options={
                'verbose_name': 'Full Reconciliation',
                'verbose_name_plural': 'Full Reconciliations',
            },
        ),
        migrations.CreateModel(
            name='AccountJournal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Journal name', max_length=100)),
                ('code', models.CharField(help_text="Journal code (e.g., 'SAL', 'PUR')", max_length=10, unique=True)),
                ('type', models.CharField(choices=[('sale', 'Sales'), ('purchase', 'Purchase'), ('cash', 'Cash'), ('bank', 'Bank'), ('general', 'Miscellaneous')], help_text='Type of journal', max_length=10)),
                ('active', models.BooleanField(default=True, help_text='Whether this journal is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(blank=True, help_text='Company this journal belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, to='account.company')),
                ('default_account_id', models.ForeignKey(help_text='Default account for this journal', on_delete=django.db.models.deletion.PROTECT, to='gl.account')),
            ],
            options={
                'verbose_name': 'Account Journal',
                'verbose_name_plural': 'Account Journals',
            },
        ),
        migrations.CreateModel(
            name='AccountMove',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='/', help_text='Move name/number (auto-generated)', max_length=100)),
                ('move_type', models.CharField(choices=[('entry', 'Journal Entry'), ('out_invoice', 'Customer Invoice'), ('out_refund', 'Customer Credit Note'), ('in_invoice', 'Vendor Bill'), ('in_refund', 'Vendor Credit Note'), ('out_receipt', 'Sales Receipt'), ('in_receipt', 'Purchase Receipt')], default='entry', help_text='Type of accounting move', max_length=20)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancel', 'Cancelled')], default='draft', help_text='Current state of the move', max_length=10)),
                ('ref', models.CharField(blank=True, help_text='Reference/memo for this move', max_length=100)),
                ('date', models.DateField(default=django.utils.timezone.now, help_text='Accounting date')),
                ('amount_total', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total amount of the move', max_digits=15)),
                ('amount_residual', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Remaining amount to be paid', max_digits=15)),
                ('currency_id', models.CharField(default='INR', help_text='Currency of this move', max_length=3)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(blank=True, help_text='Company this move belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, to='account.company')),
                ('created_uid', models.ForeignKey(help_text='User who created this move', on_delete=django.db.models.deletion.PROTECT, related_name='created_moves', to=settings.AUTH_USER_MODEL)),
                ('journal_id', models.ForeignKey(help_text='Journal this move belongs to', on_delete=django.db.models.deletion.PROTECT, to='gl.accountjournal')),
                ('partner_id', models.ForeignKey(blank=True, help_text='Customer/Vendor for this move', null=True, on_delete=django.db.models.deletion.SET_NULL, to='contacts.contact')),
            ],
            options={
                'verbose_name': 'Account Move',
                'verbose_name_plural': 'Account Moves',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='IrSequence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Sequence name (e.g., 'Account Move')", max_length=64)),
                ('code', models.CharField(help_text="Sequence code (e.g., 'account.move')", max_length=64)),
                ('prefix', models.CharField(default='', help_text="Prefix with date formatting support (e.g., 'BILL/%(year)s/')", max_length=64)),
                ('suffix', models.CharField(default='', help_text='Suffix with date formatting support', max_length=64)),
                ('number_next', models.PositiveIntegerField(default=1, help_text='Next number to be generated')),
                ('number_increment', models.PositiveIntegerField(default=1, help_text='Increment step for each new number')),
                ('padding', models.PositiveIntegerField(default=0, help_text='Zero-padding for numbers (0 = no padding)')),
                ('use_date_range', models.BooleanField(default=False, help_text='Reset sequence based on date range')),
                ('active', models.BooleanField(default=True, help_text='Whether this sequence is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(blank=True, help_text='Company this sequence belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, to='account.company')),
            ],
            options={
                'verbose_name': 'Sequence',
                'verbose_name_plural': 'Sequences',
                'unique_together': {('code', 'company_id')},
            },
        ),
        migrations.CreateModel(
            name='AccountMoveLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Description of this journal line', max_length=200)),
                ('debit', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Debit amount', max_digits=15)),
                ('credit', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Credit amount', max_digits=15)),
                ('balance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Balance (debit - credit)', max_digits=15)),
                ('currency_id', models.CharField(default='INR', help_text='Currency of this line', max_length=3)),
                ('amount_currency', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount in foreign currency', max_digits=15)),
                ('tax_base_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Base amount for tax calculation', max_digits=15)),
                ('quantity', models.DecimalField(decimal_places=3, default=Decimal('1.000'), help_text='Quantity (for product lines)', max_digits=10)),
                ('price_unit', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Unit price (for product lines)', max_digits=15)),
                ('reconciled', models.BooleanField(default=False, help_text='Whether this line is reconciled')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_id', models.ForeignKey(help_text='Account affected by this line', on_delete=django.db.models.deletion.PROTECT, to='gl.account')),
                ('company_id', models.ForeignKey(blank=True, help_text='Company this line belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, to='account.company')),
                ('full_reconcile_id', models.ForeignKey(blank=True, help_text='Full reconciliation this line belongs to', null=True, on_delete=django.db.models.deletion.SET_NULL, to='gl.accountfullreconcile')),
                ('move_id', models.ForeignKey(help_text='Parent account move', on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='gl.accountmove')),
                ('partner_id', models.ForeignKey(blank=True, help_text='Customer/Vendor for this line', null=True, on_delete=django.db.models.deletion.SET_NULL, to='contacts.contact')),
                ('product_id', models.ForeignKey(blank=True, help_text='Product for this line (if applicable)', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.product')),
                ('tax_ids', models.ManyToManyField(blank=True, help_text='Taxes applied to this line', to='sales_tax.salestax')),
            ],
            options={
                'verbose_name': 'Account Move Line',
                'verbose_name_plural': 'Account Move Lines',
                'ordering': ['move_id', 'id'],
            },
        ),
        migrations.AddField(
            model_name='accountmove',
            name='sequence_id',
            field=models.ForeignKey(blank=True, help_text='Sequence used for numbering', null=True, on_delete=django.db.models.deletion.PROTECT, to='gl.irsequence'),
        ),
        migrations.AddField(
            model_name='accountmove',
            name='write_uid',
            field=models.ForeignKey(blank=True, help_text='User who last modified this move', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='modified_moves', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='accountjournal',
            name='sequence_id',
            field=models.ForeignKey(blank=True, help_text='Sequence for move numbering', null=True, on_delete=django.db.models.deletion.PROTECT, to='gl.irsequence'),
        ),
        migrations.AlterUniqueTogether(
            name='accountjournal',
            unique_together={('code', 'company_id')},
        ),
    ]
