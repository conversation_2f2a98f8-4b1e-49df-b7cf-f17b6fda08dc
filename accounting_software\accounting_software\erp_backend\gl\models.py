"""
General Ledger Models for ERP Accounting Software

This module contains all the core accounting models following International Financial 
Reporting Standards (IFRS) and incorporating best practices from QuickBooks and 
Oracle Financials.

Key Accounting Concepts:
- Double-Entry Bookkeeping: Every transaction affects at least two accounts
- Chart of Accounts (COA): Hierarchical structure of all accounts
- Account Types: Assets, Liabilities, Equity, Revenue, Expenses
- Detail Types: More specific categorization like QuickBooks
- Journal Entries: Individual accounting transactions
- General Ledger: Complete record of all financial transactions
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import uuid

User = get_user_model()

class AccountType(models.Model):
    """
    Account Types following IFRS Financial Statement Categories
    
    This represents the main categories of accounts in accounting:
    - Assets: Resources owned by the company (Cash, Inventory, Equipment)
    - Liabilities: Debts and obligations (Accounts Payable, Loans)
    - Equity: Owner's stake in the company (Capital, Retained Earnings)
    - Revenue: Income from business operations (Sales, Service Revenue)
    - Expenses: Costs of doing business (Rent, Salaries, Utilities)
    
    Each account type has specific rules for debits/credits and financial reporting.
    """
    
    # Account type choices following IFRS structure
    ASSET = 'ASSET'
    LIABILITY = 'LIABILITY'
    EQUITY = 'EQUITY'
    REVENUE = 'REVENUE'
    EXPENSE = 'EXPENSE'
    
    TYPE_CHOICES = [
        (ASSET, 'Asset'),           # Resources owned by the company
        (LIABILITY, 'Liability'),   # Debts and obligations
        (EQUITY, 'Equity'),         # Owner's equity/capital
        (REVENUE, 'Revenue'),       # Income from operations
        (EXPENSE, 'Expense'),       # Operating costs and expenses
    ]
    
    # Basic account type information
    code = models.CharField(
        max_length=10, 
        unique=True,
        help_text="Unique code for the account type (e.g., 'ASSET', 'LIAB')"
    )
    name = models.CharField(
        max_length=100,
        help_text="Display name of the account type"
    )
    type = models.CharField(
        max_length=20, 
        choices=TYPE_CHOICES,
        help_text="The fundamental accounting category this type belongs to"
    )
    
    # Accounting behavior settings
    normal_balance = models.CharField(
        max_length=6,
        choices=[('DEBIT', 'Debit'), ('CREDIT', 'Credit')],
        help_text="Normal balance side for this account type (Debit or Credit)"
    )
    
    # Financial statement presentation
    financial_statement = models.CharField(
        max_length=20,
        choices=[
            ('BALANCE_SHEET', 'Balance Sheet'),
            ('INCOME_STATEMENT', 'Income Statement'),
            ('CASH_FLOW', 'Cash Flow Statement'),
        ],
        help_text="Which financial statement this account type appears on"
    )
    
    # Display order for reports
    sort_order = models.PositiveIntegerField(
        default=0,
        help_text="Order for displaying account types in reports and lists"
    )
    
    # System tracking
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this account type is available for new accounts"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['sort_order', 'name']
        verbose_name = "Account Type"
        verbose_name_plural = "Account Types"

    def __str__(self):
        return f"{self.code} - {self.name}"

class DetailType(models.Model):
    """
    Detail Types - Specific Account Classifications (Like QuickBooks)
    
    These provide more granular categorization within each account type,
    similar to QuickBooks detail types. This helps with:
    - Better financial reporting and analysis
    - Industry-specific account classification
    - Automated account behavior and rules
    - Integration with tax reporting
    
    Examples:
    - Asset Detail Types: Cash, Bank, Accounts Receivable, Inventory, Fixed Assets
    - Expense Detail Types: Advertising, Office Supplies, Travel, Utilities
    """
    
    # Link to the main account type
    account_type = models.ForeignKey(
        AccountType,
        on_delete=models.CASCADE,
        related_name='detail_types',
        help_text="The main account type this detail type belongs to"
    )
    
    # Detail type identification
    code = models.CharField(
        max_length=20,
        help_text="Unique code for the detail type (e.g., 'CASH_BANK', 'AR_TRADE')"
    )
    name = models.CharField(
        max_length=100,
        help_text="Display name of the detail type"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of when to use this detail type"
    )
    
    # Accounting behavior
    requires_customer = models.BooleanField(
        default=False,
        help_text="Whether accounts of this type require customer tracking"
    )
    requires_vendor = models.BooleanField(
        default=False,
        help_text="Whether accounts of this type require vendor tracking"
    )
    requires_item = models.BooleanField(
        default=False,
        help_text="Whether accounts of this type require inventory item tracking"
    )
    
    # Tax and reporting settings
    is_tax_account = models.BooleanField(
        default=False,
        help_text="Whether this is used for tax-related transactions"
    )
    default_tax_code = models.CharField(
        max_length=20,
        blank=True,
        help_text="Default tax code for transactions using this detail type"
    )
    
    # Financial statement classification
    balance_sheet_classification = models.CharField(
        max_length=20,
        blank=True,
        choices=[
            ('CURRENT', 'Current (< 1 year)'),
            ('NON_CURRENT', 'Non-Current (> 1 year)'),
            ('NOT_APPLICABLE', 'Not Applicable'),
        ],
        help_text="Balance sheet classification for assets and liabilities"
    )
    
    # Display and sorting
    sort_order = models.PositiveIntegerField(
        default=0,
        help_text="Order for displaying detail types within account type"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this detail type is available for new accounts"
    )
    
    # System tracking
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['account_type', 'sort_order', 'name']
        unique_together = ['account_type', 'code']
        verbose_name = "Detail Type"
        verbose_name_plural = "Detail Types"

    def __str__(self):
        return f"{self.account_type.code} - {self.name}"

class Account(models.Model):
    """
    Chart of Accounts (COA) - Individual General Ledger Accounts
    
    This is the core of the accounting system. Every financial transaction 
    is recorded against accounts in the Chart of Accounts. Following IFRS
    and QuickBooks best practices:
    
    - Hierarchical structure with parent/child relationships
    - Unique account numbers for easy identification
    - Detailed classification using account types and detail types
    - Support for multi-currency operations
    - Integration with budgeting and reporting
    
    Account Numbering Convention:
    1xxx - Assets (1000-1999)
    2xxx - Liabilities (2000-2999)  
    3xxx - Equity (3000-3999)
    4xxx - Revenue (4000-4999)
    5xxx-9xxx - Expenses (5000-9999)
    """
    
    # Account identification
    account_number = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique account number following company numbering scheme"
    )
    account_name = models.CharField(
        max_length=200,
        help_text="Descriptive name of the account"
    )
    
    # Account classification
    account_type = models.ForeignKey(
        AccountType,
        on_delete=models.PROTECT,
        related_name='accounts',
        help_text="The main account type (Asset, Liability, etc.)"
    )
    detail_type = models.ForeignKey(
        DetailType,
        on_delete=models.PROTECT,
        related_name='accounts',
        help_text="Specific detail type for more granular classification"
    )
    
    # Hierarchical structure for parent/child accounts
    parent_account = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='child_accounts',
        help_text="Parent account for hierarchical Chart of Accounts structure"
    )
    
    # HYBRID OPTIMIZATION: Database-level hierarchy fields for performance
    level = models.PositiveIntegerField(
        default=0,
        help_text="Hierarchy level (0=top level, 1=first sub-level, etc.)"
    )
    path = models.CharField(
        max_length=500,
        blank=True,
        help_text="Hierarchical path like '1000.1010.1011' for fast queries"
    )
    
    # Account description and usage
    description = models.TextField(
        blank=True,
        help_text="Detailed description of the account's purpose and usage"
    )
    
    # Currency settings
    currency = models.CharField(
        max_length=3,
        default='INR',
        help_text="Default currency for this account (ISO 4217 code)"
    )
    
    # Account behavior settings
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this account is available for new transactions"
    )
    is_header_account = models.BooleanField(
        default=False,
        help_text="Whether this is a header account (summary only, no transactions)"
    )
    
    # Opening balance information
    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Opening balance when account was created"
    )
    opening_balance_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date of the opening balance"
    )
    
    # Tax and regulatory settings
    tax_line = models.CharField(
        max_length=100,
        blank=True,
        help_text="Tax form line this account maps to for tax reporting"
    )
    
    # Banking integration
    bank_account_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Associated bank account number for cash/bank accounts"
    )
    bank_routing_number = models.CharField(
        max_length=20,
        blank=True,
        help_text="Bank routing number for cash/bank accounts"
    )
    
    # System and audit fields
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_accounts',
        help_text="User who created this account"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Version control for accounting compliance
    version = models.PositiveIntegerField(
        default=1,
        help_text="Version number for audit trail purposes"
    )

    class Meta:
        ordering = ['account_number']
        verbose_name = "Account"
        verbose_name_plural = "Chart of Accounts"

    def __str__(self):
        return f"{self.account_number} - {self.account_name}"
    
    def save(self, *args, **kwargs):
        """
        HYBRID OPTIMIZATION: Auto-calculate level and path for performance
        This gives us database-level query optimization while keeping business logic in Python
        """
        # Calculate hierarchy level and path
        if self.parent_account:
            self.level = self.parent_account.level + 1
            if self.parent_account.path:
                self.path = f"{self.parent_account.path}.{self.account_number}"
            else:
                self.path = f"{self.parent_account.account_number}.{self.account_number}"
        else:
            self.level = 0
            self.path = self.account_number
        
        super().save(*args, **kwargs)
        
        # Update child accounts if this account's path changed
        if self.pk:  # Only if this is an update, not a new record
            self._update_child_paths()
    
    def _update_child_paths(self):
        """Update all child account paths when parent path changes"""
        for child in self.child_accounts.all():
            child.save()  # This will trigger recalculation of child's path
    
    def get_current_balance(self, as_of_date=None):
        """
        Calculate the current balance of this account as of a specific date.
        
        This follows double-entry bookkeeping rules:
        - Asset and Expense accounts: Debits increase, Credits decrease
        - Liability, Equity, and Revenue accounts: Credits increase, Debits decrease
        """
        from django.db.models import Sum
        from django.utils import timezone
        
        if as_of_date is None:
            as_of_date = timezone.now().date()
        
        # Get all journal entry lines for this account up to the specified date
        journal_lines = self.journal_lines.filter(
            journal_entry__transaction_date__lte=as_of_date,
            journal_entry__status='POSTED'
        )
        
        # Calculate total debits and credits
        total_debits = journal_lines.aggregate(
            total=Sum('debit_amount')
        )['total'] or Decimal('0.00')
        
        total_credits = journal_lines.aggregate(
            total=Sum('credit_amount')
        )['total'] or Decimal('0.00')
        
        # Calculate balance based on account type's normal balance
        if self.account_type.normal_balance == 'DEBIT':
            # Assets and Expenses: Debit - Credit
            balance = self.opening_balance + total_debits - total_credits
        else:
            # Liabilities, Equity, and Revenue: Credit - Debit
            balance = self.opening_balance + total_credits - total_debits
            
        return balance
    
    def get_full_account_path(self):
        """Get the full hierarchical path of this account."""
        if self.parent_account:
            return f"{self.parent_account.get_full_account_path()} > {self.account_name}"
        return self.account_name
    
    def clean(self):
        """Validate account data before saving."""
        from django.core.exceptions import ValidationError
        
        # Ensure detail type belongs to the selected account type
        if self.detail_type and self.detail_type.account_type != self.account_type:
            raise ValidationError(
                f"Detail type '{self.detail_type}' does not belong to account type '{self.account_type}'"
            )
        
        # Prevent circular parent relationships
        if self.parent_account:
            current = self.parent_account
            while current:
                if current == self:
                    raise ValidationError("Account cannot be its own parent (circular reference)")
                current = current.parent_account

class JournalEntry(models.Model):
    """
    Journal Entries - Individual Accounting Transactions
    
    This represents individual transactions in the accounting system following
    double-entry bookkeeping principles. Every journal entry must:
    - Have at least two journal entry lines (one debit, one credit)
    - Total debits must equal total credits
    - Include proper documentation and references
    - Follow audit trail requirements
    
    Journal entries are the foundation of all financial reporting and must
    maintain data integrity for accurate financial statements.
    """
    
    # Entry status workflow
    DRAFT = 'DRAFT'
    PENDING = 'PENDING'
    POSTED = 'POSTED'
    REVERSED = 'REVERSED'
    
    STATUS_CHOICES = [
        (DRAFT, 'Draft'),           # Entry is being prepared
        (PENDING, 'Pending'),       # Entry is ready for review
        (POSTED, 'Posted'),         # Entry is finalized in GL
        (REVERSED, 'Reversed'),     # Entry has been reversed
    ]
    
    # Journal entry identification
    entry_number = models.CharField(
        max_length=50,
        unique=True,
        help_text="Unique journal entry number for tracking and reference"
    )
    
    # Transaction details
    transaction_date = models.DateField(
        help_text="The actual date when the business transaction occurred"
    )
    posting_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date when the entry was posted to the general ledger"
    )
    
    # Entry classification
    entry_type = models.CharField(
        max_length=50,
        choices=[
            ('GENERAL', 'General Journal Entry'),
            ('ADJUSTING', 'Adjusting Entry'),
            ('CLOSING', 'Closing Entry'),
            ('REVERSING', 'Reversing Entry'),
            ('RECURRING', 'Recurring Entry'),
        ],
        default='GENERAL',
        help_text="Type of journal entry for reporting and processing"
    )
    
    # Voucher type for different forms
    voucher_type = models.ForeignKey(
        'VoucherType',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='journal_entries',
        help_text="Type of voucher/form used for this entry"
    )
    
    # Documentation and references
    description = models.CharField(
        max_length=500,
        help_text="Clear description of the business transaction"
    )
    reference_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="External reference number (invoice, receipt, etc.)"
    )
    memo = models.TextField(
        blank=True,
        help_text="Additional notes or explanation for the entry"
    )
    
    # Source document tracking for better audit trail
    source_document_type = models.CharField(
        max_length=20,
        blank=True,
        choices=[
            ('GRN', 'Goods Receipt Note'),
            ('PO', 'Purchase Order'),
            ('INVOICE', 'Invoice'),
            ('BILL', 'Bill'),
            ('PAYMENT', 'Payment'),
            ('RECEIPT', 'Receipt'),
            ('MANUAL', 'Manual Entry'),
        ],
        help_text="Type of source document that generated this journal entry"
    )
    source_document_id = models.CharField(
        max_length=50,
        blank=True,
        help_text="ID of the source document (GRN number, PO number, etc.)"
    )
    source_document_reference = models.CharField(
        max_length=100,
        blank=True,
        help_text="Additional reference information for the source document"
    )
    
    # Status and workflow
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=DRAFT,
        help_text="Current status of the journal entry"
    )
    
    # Currency handling
    currency = models.CharField(
        max_length=3,
        default='USD',  # Will be dynamically set to company's functional currency
        help_text="Currency of the transaction (ISO 4217 code)"
    )
    exchange_rate = models.DecimalField(
        max_digits=10,
        decimal_places=6,
        default=Decimal('1.000000'),
        help_text="Exchange rate from transaction currency to functional currency"
    )
    
    # Reporting currency amounts (calculated fields)
    reporting_currency_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Total amount converted to reporting currency"
    )
    reporting_exchange_rate = models.DecimalField(
        max_digits=10,
        decimal_places=6,
        default=Decimal('1.000000'),
        help_text="Exchange rate from functional currency to reporting currency"
    )
    
    # Tax calculations for payments and receipts
    gross_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Gross amount before tax calculations"
    )
    sales_tax_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Total sales tax amount"
    )
    tds_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Total TDS deduction amount"
    )
    net_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Net amount after all tax calculations"
    )
    
    # Tax references
    input_tax = models.ForeignKey(
        'sales_tax.SalesTax',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='input_journal_entries',
        help_text="Input tax (for payments)"
    )
    output_tax = models.ForeignKey(
        'sales_tax.SalesTax',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='output_journal_entries',
        help_text="Output tax (for receipts)"
    )
    tds_rate = models.ForeignKey(
        'tds.TDS',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_entries',
        help_text="TDS rate applied to this entry"
    )

    # Reversal handling
    reversed_entry = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reversing_entries',
        help_text="Original entry that this entry reverses"
    )
    reversal_reason = models.TextField(
        blank=True,
        help_text="Reason for reversing the original entry"
    )
    
    # Audit and tracking fields
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_journal_entries',
        help_text="User who created this journal entry"
    )
    posted_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='posted_journal_entries',
        help_text="User who posted this journal entry"
    )
    
    # System timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    posted_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when the entry was posted"
    )

    class Meta:
        ordering = ['-transaction_date', '-entry_number']
        verbose_name = "Journal Entry"
        verbose_name_plural = "Journal Entries"

    def __str__(self):
        return f"{self.entry_number} - {self.description} ({self.transaction_date})"
    
    def get_total_debits(self):
        """Calculate total debit amount for this journal entry."""
        return sum(line.debit_amount for line in self.journal_lines.all())
    
    def get_total_credits(self):
        """Calculate total credit amount for this journal entry."""
        return sum(line.credit_amount for line in self.journal_lines.all())
    
    def is_balanced(self):
        """Check if total debits equal total credits (double-entry rule)."""
        return self.get_total_debits() == self.get_total_credits()
    
    def can_be_posted(self):
        """Check if entry meets all requirements for posting."""
        return (
            self.status == self.DRAFT and
            self.is_balanced() and
            self.journal_lines.count() >= 2
        )
    
    def calculate_taxes(self):
        """Calculate tax amounts based on tax rates and gross amount"""
        if not self.gross_amount:
            return
        
        # Calculate sales tax
        if self.input_tax:
            self.sales_tax_amount = (self.gross_amount * self.input_tax.rate) / 100
        elif self.output_tax:
            self.sales_tax_amount = (self.gross_amount * self.output_tax.rate) / 100
        else:
            self.sales_tax_amount = Decimal('0.00')
        
        # Calculate TDS
        if self.tds_rate and self.gross_amount >= (self.tds_rate.threshold_limit or 0):
            self.tds_amount = (self.gross_amount * self.tds_rate.rate) / 100
        else:
            self.tds_amount = Decimal('0.00')
        
        # Calculate net amount
        if self.voucher_type and self.voucher_type.voucher_code == 'PV':  # Payment Voucher
            # For payments: Net = Gross + Input Tax - TDS
            self.net_amount = self.gross_amount + self.sales_tax_amount - self.tds_amount
        elif self.voucher_type and self.voucher_type.voucher_code == 'RV':  # Receipt Voucher
            # For receipts: Net = Gross - Output Tax - TDS
            self.net_amount = self.gross_amount - self.sales_tax_amount - self.tds_amount
        else:
            # For general entries: Net = Gross
            self.net_amount = self.gross_amount
    
    def generate_tax_journal_lines(self):
        """Generate automatic journal lines for tax calculations"""
        lines_to_create = []
        line_number = self.journal_lines.count() + 1
        
        # Calculate total sales tax from line items
        total_sales_tax = Decimal('0.00')
        sales_tax_details = {}  # Group by tax rate for detailed descriptions
        
        for line in self.journal_lines.all():
            if line.sales_tax_amount > 0:
                total_sales_tax += line.sales_tax_amount
                tax_key = f"{line.sales_tax.description}_{line.sales_tax.rate}_{line.sales_tax.tax_type}"
                if tax_key not in sales_tax_details:
                    sales_tax_details[tax_key] = {
                        'amount': Decimal('0.00'),
                        'tax': line.sales_tax,
                        'lines': []
                    }
                sales_tax_details[tax_key]['amount'] += line.sales_tax_amount
                sales_tax_details[tax_key]['lines'].append(line)
        
        # Create sales tax journal entries based on line-level calculations
        if total_sales_tax > 0:
            # Get the Sales Tax Payable account (ID 67 as found earlier)
            try:
                from .models import Account
                sales_tax_payable_account_id = 67  # Sales Tax Payable account
                
                for tax_key, tax_info in sales_tax_details.items():
                    tax_amount = tax_info['amount']
                    sales_tax = tax_info['tax']
                    
                    if sales_tax.tax_type == 'input':
                        # Input tax (payments) - debit Sales Tax Payable (reduces liability)
                        lines_to_create.append({
                            'line_number': line_number,
                            'account_id': sales_tax_payable_account_id,
                            'description': f"Input Tax - {sales_tax.description} @ {sales_tax.rate}%",
                            'debit_amount': tax_amount,
                            'credit_amount': Decimal('0.00'),
                        })
                    else:  # output tax
                        # Output tax (receipts) - credit Sales Tax Payable (increases liability)
                        lines_to_create.append({
                            'line_number': line_number,
                            'account_id': sales_tax_payable_account_id,
                            'description': f"Output Tax - {sales_tax.description} @ {sales_tax.rate}%",
                            'debit_amount': Decimal('0.00'),
                            'credit_amount': tax_amount,
                        })
                    line_number += 1
                    
            except Exception as e:
                # Log error but don't fail the entire process
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error creating sales tax journal lines: {e}")
        
        # TDS line (existing logic)
        if self.tds_amount > 0:
            if self.tds_rate.is_liability:
                # TDS payable - credit TDS Payable account
                lines_to_create.append({
                    'line_number': line_number,
                    'description': f"TDS @ {self.tds_rate.rate}% - {self.tds_rate.section}",
                    'debit_amount': Decimal('0.00'),
                    'credit_amount': self.tds_amount,
                })
            else:
                # TDS receivable - debit TDS Receivable account
                lines_to_create.append({
                    'line_number': line_number,
                    'description': f"TDS @ {self.tds_rate.rate}% - {self.tds_rate.section}",
                    'debit_amount': self.tds_amount,
                    'credit_amount': Decimal('0.00'),
                })
        
        return lines_to_create

    def get_company_currencies(self):
        """Get the company's functional and reporting currencies."""
        try:
            from account.models import Company
            company = Company.objects.first()
            if company:
                return company.functional_currency, company.reporting_currency
        except (ImportError, Exception):
            pass
        return 'USD', 'USD'  # Default fallback
    
    def update_currency_amounts(self):
        """Update reporting currency amounts based on exchange rates."""
        try:
            functional_currency, reporting_currency = self.get_company_currencies()
            
            # Get total amount in functional currency
            total_functional_amount = self.get_total_debits()
            
            # Convert to reporting currency if different
            if functional_currency != reporting_currency:
                self.reporting_currency_amount = total_functional_amount * (self.reporting_exchange_rate or Decimal('1.000000'))
            else:
                self.reporting_currency_amount = total_functional_amount
                self.reporting_exchange_rate = Decimal('1.000000')
        except Exception:
            # If anything fails, set safe defaults
            self.reporting_currency_amount = Decimal('0.00')
            self.reporting_exchange_rate = Decimal('1.000000')
    
    def set_default_currency(self):
        """Set default currency from company settings if not already set."""
        if not self.currency:
            functional_currency, _ = self.get_company_currencies()
            self.currency = functional_currency

    def post_entry(self, posted_by_user):
        """Post the journal entry to the general ledger."""
        from django.utils import timezone
        
        if not self.can_be_posted():
            raise ValueError("Journal entry cannot be posted - requirements not met")
        
        self.status = self.POSTED
        self.posted_by = posted_by_user
        self.posted_at = timezone.now()
        self.posting_date = timezone.now().date()
        self.save()

class JournalEntryLine(models.Model):
    """
    Journal Entry Lines - Individual Account Postings
    
    Each line represents a single debit or credit to a specific account
    within a journal entry. This is where the actual double-entry
    bookkeeping is implemented:
    
    - Each line affects exactly one account
    - Each line has either a debit or credit amount (not both)
    - All lines in a journal entry must balance (debits = credits)
    - Lines can include additional tracking (customer, vendor, project, etc.)
    """
    
    # Link to parent journal entry
    journal_entry = models.ForeignKey(
        JournalEntry,
        on_delete=models.CASCADE,
        related_name='journal_lines',
        help_text="The journal entry this line belongs to"
    )
    
    # Account being affected
    account = models.ForeignKey(
        Account,
        on_delete=models.PROTECT,
        related_name='journal_lines',
        help_text="The Chart of Accounts account being debited or credited"
    )
    
    # Debit and credit amounts (only one should have a value)
    debit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Debit amount (leave 0.00 if this is a credit line)"
    )
    credit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Credit amount (leave 0.00 if this is a debit line)"
    )
    
    # Line description and reference
    description = models.CharField(
        max_length=500,
        help_text="Description specific to this account posting"
    )
    memo = models.TextField(
        blank=True,
        help_text="Additional notes for this specific line"
    )
    
    # Additional tracking dimensions (for detailed reporting)
    customer = models.CharField(
        max_length=100,
        blank=True,
        help_text="Customer associated with this transaction line"
    )
    vendor = models.CharField(
        max_length=100,
        blank=True,
        help_text="Vendor associated with this transaction line"
    )
    project = models.ForeignKey(
        'Project',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_lines',
        help_text="Project associated with this transaction line"
    )
    product = models.ForeignKey(
        'Product',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_lines',
        help_text="Product/service associated with this transaction line"
    )
    department = models.CharField(
        max_length=100,
        blank=True,
        help_text="Department associated with this transaction line"
    )
    location = models.CharField(
        max_length=100,
        blank=True,
        help_text="Location or branch associated with this transaction line"
    )
    
    # Quantity and unit price for detailed tracking
    quantity = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        null=True,
        blank=True,
        help_text="Quantity for product/service lines"
    )
    unit_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Unit price for product/service lines"
    )
    
    # Line sequence for consistent ordering
    line_number = models.PositiveIntegerField(
        help_text="Sequence number of this line within the journal entry"
    )
    
    # Currency handling (for multi-currency environments)
    original_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Original amount in transaction currency before conversion"
    )
    original_currency = models.CharField(
        max_length=3,
        blank=True,
        help_text="Original currency code if different from functional currency"
    )
    
    # Sales Tax information for line-level tax calculation
    sales_tax = models.ForeignKey(
        'sales_tax.SalesTax',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_entry_lines',
        help_text="Sales tax applied to this line item"
    )
    sales_tax_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Sales tax amount calculated for this line"
    )
    taxable_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Amount subject to sales tax (before tax calculation)"
    )
    
    # System tracking
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['journal_entry', 'line_number']
        unique_together = ['journal_entry', 'line_number']
        verbose_name = "Journal Entry Line"
        verbose_name_plural = "Journal Entry Lines"

    def __str__(self):
        amount = self.debit_amount if self.debit_amount > 0 else self.credit_amount
        side = "Dr" if self.debit_amount > 0 else "Cr"
        return f"{self.account.account_number} - {side} {amount}"
    
    def clean(self):
        """Validate journal entry line data."""
        from django.core.exceptions import ValidationError
        
        # Ensure only debit OR credit has a value (not both, not neither)
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError("A line cannot have both debit and credit amounts")
        
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError("A line must have either a debit or credit amount")
        
        # Ensure the account is not a header account
        if self.account.is_header_account:
            raise ValidationError("Cannot post transactions to header accounts")
    
    def get_amount(self):
        """Get the absolute amount of this line (debit or credit)."""
        return self.debit_amount if self.debit_amount > 0 else self.credit_amount
    
    def is_debit(self):
        """Check if this is a debit line."""
        return self.debit_amount > 0
    
    def is_credit(self):
        """Check if this is a credit line."""
        return self.credit_amount > 0
    
    def calculate_line_sales_tax(self):
        """Calculate sales tax for this line item."""
        if not self.sales_tax or not self.taxable_amount:
            self.sales_tax_amount = Decimal('0.00')
            return
        
        # Calculate tax based on taxable amount and tax rate
        self.sales_tax_amount = (self.taxable_amount * self.sales_tax.rate) / 100
    
    def save(self, *args, **kwargs):
        """Override save to calculate sales tax automatically."""
        # Calculate sales tax if applicable
        self.calculate_line_sales_tax()
        super().save(*args, **kwargs)

class Project(models.Model):
    """
    Project/Job Tracking for Journal Entries
    
    This allows tracking of transactions by project or job for better
    cost allocation and project profitability analysis.
    """
    project_code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique project code for identification"
    )
    project_name = models.CharField(
        max_length=200,
        help_text="Descriptive name of the project"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of the project"
    )
    start_date = models.DateField(
        null=True,
        blank=True,
        help_text="Project start date"
    )
    end_date = models.DateField(
        null=True,
        blank=True,
        help_text="Expected or actual project end date"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this project is available for new transactions"
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_projects'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['project_code']
        verbose_name = "Project"
        verbose_name_plural = "Projects"

    def __str__(self):
        return f"{self.project_code} - {self.project_name}"


class Product(models.Model):
    """
    Product/Service Tracking for Journal Entries
    
    This allows tracking of transactions by product or service for better
    profitability analysis and inventory management.
    """
    product_code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Unique product code for identification"
    )
    product_name = models.CharField(
        max_length=200,
        help_text="Descriptive name of the product/service"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of the product/service"
    )
    product_type = models.CharField(
        max_length=20,
        choices=[
            ('PRODUCT', 'Physical Product'),
            ('SERVICE', 'Service'),
            ('DIGITAL', 'Digital Product'),
        ],
        default='PRODUCT',
        help_text="Type of product or service"
    )
    unit_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Standard unit price"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this product is available for transactions"
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_products'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['product_code']
        verbose_name = "Product"
        verbose_name_plural = "Products"

    def __str__(self):
        return f"{self.product_code} - {self.product_name}"


class VoucherType(models.Model):
    """
    Voucher Types for different Journal Entry forms
    
    This defines the different types of vouchers/forms available:
    - General Journal Entry (JV)
    - Payment Voucher (PV) 
    - Receipt Voucher (RV)
    - Bank/Cash Voucher (BV/CV)
    """
    
    VOUCHER_TYPES = [
        ('JV', 'Journal Voucher - General Entry'),
        ('PV', 'Payment Voucher - Payments Made'),
        ('RV', 'Receipt Voucher - Receipts Received'),
        ('BV', 'Bank Voucher - Bank Transactions'),
        ('CV', 'Cash Voucher - Cash Transactions'),
    ]
    
    voucher_code = models.CharField(
        max_length=5,
        choices=VOUCHER_TYPES,
        unique=True,
        help_text="Voucher type code"
    )
    voucher_name = models.CharField(
        max_length=100,
        help_text="Display name of the voucher type"
    )
    description = models.TextField(
        blank=True,
        help_text="Description of when to use this voucher type"
    )
    number_prefix = models.CharField(
        max_length=10,
        help_text="Prefix for auto-generated voucher numbers"
    )
    next_number = models.PositiveIntegerField(
        default=1,
        help_text="Next number in sequence for this voucher type"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this voucher type is available"
    )
    
    class Meta:
        ordering = ['voucher_code']
        verbose_name = "Voucher Type"
        verbose_name_plural = "Voucher Types"
    
    def __str__(self):
        return f"{self.voucher_code} - {self.voucher_name}"
    
    def get_next_voucher_number(self):
        """Generate next voucher number and increment counter"""
        current_number = self.next_number
        self.next_number += 1
        self.save()
        return f"{self.number_prefix}{current_number:04d}"

class RecurringJournalEntry(models.Model):
    """
    Recurring Journal Entry Templates
    
    This model stores templates for journal entries that need to be created
    repeatedly (monthly rent, salaries, etc.). Users can create templates
    and then generate actual journal entries from them.
    """
    
    FREQUENCY_CHOICES = [
        ('DAILY', 'Daily'),
        ('WEEKLY', 'Weekly'),
        ('MONTHLY', 'Monthly'),
        ('QUARTERLY', 'Quarterly'),
        ('YEARLY', 'Yearly'),
    ]
    
    # Template identification
    template_name = models.CharField(
        max_length=200,
        help_text="Descriptive name for this recurring template"
    )
    description = models.CharField(
        max_length=500,
        help_text="Description for journal entries created from this template"
    )
    reference_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Reference number template"
    )
    memo = models.TextField(
        blank=True,
        help_text="Memo template for generated entries"
    )
    
    # Recurrence settings
    frequency = models.CharField(
        max_length=20,
        choices=FREQUENCY_CHOICES,
        default='MONTHLY',
        help_text="How often to generate journal entries"
    )
    start_date = models.DateField(
        help_text="Date to start generating recurring entries"
    )
    end_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date to stop generating entries (leave blank for indefinite)"
    )
    next_generation_date = models.DateField(
        null=True,
        blank=True,
        help_text="Next date when entry should be generated"
    )
    
    # Currency and amounts
    currency = models.CharField(
        max_length=3,
        default='INR',
        help_text="Currency for this recurring template"
    )
    
    # Template status
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this template is active for generation"
    )
    
    # Audit fields
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_recurring_entries',
        help_text="User who created this template"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Statistics
    total_generated = models.PositiveIntegerField(
        default=0,
        help_text="Total number of journal entries generated from this template"
    )
    last_generated_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the last journal entry was generated"
    )
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = "Recurring Journal Entry"
        verbose_name_plural = "Recurring Journal Entries"

    def __str__(self):
        return f"{self.template_name} ({self.frequency})"

    def calculate_next_generation_date(self):
        """Calculate the next date when entry should be generated"""
        from datetime import timedelta
        try:
            from dateutil.relativedelta import relativedelta
        except ImportError:
            # Fallback if dateutil is not available
            from datetime import timedelta
            relativedelta = lambda months=0, years=0: timedelta(days=30*months + 365*years)
        
        if not self.next_generation_date:
            self.next_generation_date = self.start_date
            return self.next_generation_date
        
        current_date = self.next_generation_date
        
        if self.frequency == 'DAILY':
            next_date = current_date + timedelta(days=1)
        elif self.frequency == 'WEEKLY':
            next_date = current_date + timedelta(weeks=1)
        elif self.frequency == 'MONTHLY':
            next_date = current_date + relativedelta(months=1)
        elif self.frequency == 'QUARTERLY':
            next_date = current_date + relativedelta(months=3)
        elif self.frequency == 'YEARLY':
            next_date = current_date + relativedelta(years=1)
        else:
            next_date = current_date + relativedelta(months=1)  # Default to monthly
        
        self.next_generation_date = next_date
        return next_date

    def generate_journal_entry(self, generation_date=None):
        """Generate a journal entry from this template"""
        from datetime import date
        
        if not generation_date:
            generation_date = date.today()
        
        # Create the journal entry
        journal_entry = JournalEntry.objects.create(
            entry_number=f"REC-{self.id}-{generation_date.strftime('%Y%m%d')}",
            transaction_date=generation_date,
            description=self.description,
            reference_number=self.reference_number,
            memo=self.memo,
            currency=self.currency,
            entry_type='RECURRING',
            status='DRAFT',
            created_by=self.created_by
        )
        
        # Copy the journal lines
        for template_line in self.recurring_lines.all():
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                account=template_line.account,
                debit_amount=template_line.debit_amount,
                credit_amount=template_line.credit_amount,
                description=template_line.description,
                memo=template_line.memo,
                line_number=template_line.line_number
            )
        
        # Update statistics
        self.total_generated += 1
        self.last_generated_at = timezone.now()
        self.calculate_next_generation_date()
        self.save()
        
        return journal_entry

class RecurringJournalEntryLine(models.Model):
    """
    Template lines for recurring journal entries
    """
    
    # Link to recurring template
    recurring_entry = models.ForeignKey(
        RecurringJournalEntry,
        on_delete=models.CASCADE,
        related_name='recurring_lines',
        help_text="The recurring template this line belongs to"
    )
    
    # Account and amounts
    account = models.ForeignKey(
        Account,
        on_delete=models.PROTECT,
        related_name='recurring_journal_lines',
        help_text="The account for this line"
    )
    debit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Debit amount template"
    )
    credit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text="Credit amount template"
    )
    
    # Line details
    description = models.CharField(
        max_length=500,
        help_text="Description template for this line"
    )
    memo = models.TextField(
        blank=True,
        help_text="Memo template for this line"
    )
    line_number = models.PositiveIntegerField(
        help_text="Line sequence number"
    )
    
    # System tracking
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['recurring_entry', 'line_number']
        unique_together = ['recurring_entry', 'line_number']
        verbose_name = "Recurring Journal Entry Line"
        verbose_name_plural = "Recurring Journal Entry Lines"

    def __str__(self):
        return f"{self.recurring_entry.template_name} - Line {self.line_number}"

    def clean(self):
        """Validate that line has either debit or credit amount, not both"""
        from django.core.exceptions import ValidationError
        
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError("A line cannot have both debit and credit amounts")
        
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError("A line must have either a debit or credit amount")


class IrSequence(models.Model):
    """
    Odoo-style Sequence Management (ir.sequence equivalent)

    This provides centralized sequence management following Odoo's approach
    for all document types in the ERP system.
    """

    # Sequence identification (Odoo naming convention)
    name = models.CharField(
        max_length=64,
        help_text="Sequence name (e.g., 'Account Move')"
    )
    code = models.CharField(
        max_length=64,
        help_text="Sequence code (e.g., 'account.move')"
    )

    # Number format settings (Odoo-style)
    prefix = models.CharField(
        max_length=64,
        default='',
        help_text="Prefix with date formatting support (e.g., 'BILL/%(year)s/')"
    )
    suffix = models.CharField(
        max_length=64,
        default='',
        help_text="Suffix with date formatting support"
    )

    # Sequence control (Odoo approach)
    number_next = models.PositiveIntegerField(
        default=1,
        help_text="Next number to be generated"
    )
    number_increment = models.PositiveIntegerField(
        default=1,
        help_text="Increment step for each new number"
    )
    padding = models.PositiveIntegerField(
        default=0,
        help_text="Zero-padding for numbers (0 = no padding)"
    )

    # Odoo-style date reset functionality
    use_date_range = models.BooleanField(
        default=False,
        help_text="Reset sequence based on date range"
    )

    # System settings (Odoo naming)
    active = models.BooleanField(
        default=True,
        help_text="Whether this sequence is active"
    )

    # Multi-company support (like Odoo)
    company_id = models.ForeignKey(
        'account.Company',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Company this sequence belongs to"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Sequence"
        verbose_name_plural = "Sequences"
        unique_together = [['code', 'company_id']]

    def __str__(self):
        return f"{self.name} ({self.code})"

    def _get_prefix_suffix(self, date=None):
        """
        Odoo-style prefix/suffix formatting with date support
        """
        if date is None:
            date = timezone.now().date()

        format_values = {
            'year': date.year,
            'month': date.month,
            'day': date.day,
            'y': str(date.year)[-2:],  # 2-digit year
        }

        try:
            formatted_prefix = self.prefix % format_values if self.prefix else ''
            formatted_suffix = self.suffix % format_values if self.suffix else ''
        except (KeyError, ValueError):
            # Fallback if formatting fails
            formatted_prefix = self.prefix
            formatted_suffix = self.suffix

        return formatted_prefix, formatted_suffix

    def next_by_id(self, date=None):
        """
        Odoo-style method name for getting next number
        Thread-safe implementation with date-based formatting
        """
        from django.db import transaction

        with transaction.atomic():
            # Lock this record to prevent concurrent access
            sequence = IrSequence.objects.select_for_update().get(pk=self.pk)

            # Get formatted prefix and suffix
            prefix, suffix = sequence._get_prefix_suffix(date)

            # Generate the formatted number
            if sequence.padding > 0:
                number_str = f"{sequence.number_next:0{sequence.padding}d}"
            else:
                number_str = str(sequence.number_next)

            formatted_number = f"{prefix}{number_str}{suffix}"

            # Update the next number
            sequence.number_next += sequence.number_increment
            sequence.save()

            return formatted_number


class AccountMove(models.Model):
    """
    Odoo-style Account Move - Universal Accounting Document

    This is the central model for all accounting documents in Odoo:
    - Customer Invoices (move_type='out_invoice')
    - Vendor Bills (move_type='in_invoice')
    - Credit Notes (move_type='out_refund', 'in_refund')
    - Journal Entries (move_type='entry')
    - Payments (move_type='out_receipt', 'in_receipt')
    """

    # Odoo move types
    MOVE_TYPE_CHOICES = [
        ('entry', 'Journal Entry'),
        ('out_invoice', 'Customer Invoice'),
        ('out_refund', 'Customer Credit Note'),
        ('in_invoice', 'Vendor Bill'),
        ('in_refund', 'Vendor Credit Note'),
        ('out_receipt', 'Sales Receipt'),
        ('in_receipt', 'Purchase Receipt'),
    ]

    # Odoo state management
    STATE_CHOICES = [
        ('draft', 'Draft'),
        ('posted', 'Posted'),
        ('cancel', 'Cancelled'),
    ]

    # Core fields (Odoo naming convention)
    name = models.CharField(
        max_length=100,
        default='/',
        help_text="Move name/number (auto-generated)"
    )
    move_type = models.CharField(
        max_length=20,
        choices=MOVE_TYPE_CHOICES,
        default='entry',
        help_text="Type of accounting move"
    )
    state = models.CharField(
        max_length=10,
        choices=STATE_CHOICES,
        default='draft',
        help_text="Current state of the move"
    )

    # Document information
    ref = models.CharField(
        max_length=100,
        blank=True,
        help_text="Reference/memo for this move"
    )
    date = models.DateField(
        default=timezone.now,
        help_text="Accounting date"
    )

    # Partner information (Odoo uses 'partner' for customers/vendors)
    partner_id = models.ForeignKey(
        'contacts.Contact',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Customer/Vendor for this move"
    )

    # Financial information
    amount_total = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Total amount of the move"
    )
    amount_residual = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Remaining amount to be paid"
    )

    # Currency (Odoo approach)
    currency_id = models.CharField(
        max_length=3,
        default='INR',
        help_text="Currency of this move"
    )

    # Journal (Odoo concept)
    journal_id = models.ForeignKey(
        'AccountJournal',
        on_delete=models.PROTECT,
        help_text="Journal this move belongs to"
    )

    # Company (multi-company like Odoo)
    company_id = models.ForeignKey(
        'account.Company',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Company this move belongs to"
    )

    # Sequence reference
    sequence_id = models.ForeignKey(
        IrSequence,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="Sequence used for numbering"
    )

    # System fields
    created_uid = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_moves',
        null=True,
        blank=True,
        help_text="User who created this move"
    )
    write_uid = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='modified_moves',
        null=True,
        blank=True,
        help_text="User who last modified this move"
    )
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Account Move"
        verbose_name_plural = "Account Moves"
        ordering = ['-date', '-id']

    def __str__(self):
        return f"{self.name} - {self.get_move_type_display()}"

    def action_post(self):
        """
        Odoo-style method to post the move
        """
        if self.state != 'draft':
            raise ValueError("Only draft moves can be posted")

        # Generate sequence number if needed
        if self.name == '/' and self.sequence_id:
            self.name = self.sequence_id.next_by_id(self.date)

        # Validate move (ensure debits = credits)
        self._validate_move()

        # Update state
        self.state = 'posted'
        self.save()

        # Update partner balances, etc.
        self._post_process()

    def _validate_move(self):
        """
        Validate that debits equal credits (Odoo approach)
        """
        total_debit = sum(line.debit for line in self.line_ids.all())
        total_credit = sum(line.credit for line in self.line_ids.all())

        if abs(total_debit - total_credit) > 0.01:  # Allow for rounding
            raise ValueError(f"Debits ({total_debit}) must equal Credits ({total_credit})")

    def _post_process(self):
        """
        Post-processing after posting (update balances, etc.)
        """
        # Update account balances, partner balances, etc.
        # This would be implemented based on specific business needs
        pass

    def _calculate_totals(self):
        """
        Calculate and update move totals from lines
        """
        from decimal import Decimal

        total_debit = sum(line.debit for line in self.line_ids.all())
        total_credit = sum(line.credit for line in self.line_ids.all())

        # For customer invoices/bills, total is the receivable/payable amount
        if self.move_type in ['out_invoice', 'in_invoice']:
            self.amount_total = max(total_debit, total_credit)
        else:
            self.amount_total = abs(total_debit - total_credit)

        # Amount residual is initially the same as total (before payments)
        self.amount_residual = self.amount_total

        self.save()


class AccountMoveLine(models.Model):
    """
    Odoo-style Account Move Line - Individual Journal Entry Lines

    This represents individual debit/credit lines within an AccountMove.
    Every line affects a specific account and may be linked to a partner.
    """

    # Link to parent move
    move_id = models.ForeignKey(
        AccountMove,
        on_delete=models.CASCADE,
        related_name='line_ids',
        help_text="Parent account move"
    )

    # Account information
    account_id = models.ForeignKey(
        Account,
        on_delete=models.PROTECT,
        help_text="Account affected by this line"
    )

    # Partner (customer/vendor) - Odoo approach
    partner_id = models.ForeignKey(
        'contacts.Contact',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Customer/Vendor for this line"
    )

    # Line description
    name = models.CharField(
        max_length=200,
        help_text="Description of this journal line"
    )

    # Amounts (Odoo uses separate debit/credit fields)
    debit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Debit amount"
    )
    credit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Credit amount"
    )

    # Balance (computed field in Odoo)
    balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Balance (debit - credit)"
    )

    # Currency information
    currency_id = models.CharField(
        max_length=3,
        default='INR',
        help_text="Currency of this line"
    )
    amount_currency = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Amount in foreign currency"
    )

    # Tax information (Odoo approach)
    tax_ids = models.ManyToManyField(
        'sales_tax.SalesTax',
        blank=True,
        help_text="Taxes applied to this line"
    )
    tax_base_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Base amount for tax calculation"
    )

    # Product information (for invoice lines)
    product_id = models.ForeignKey(
        'sales.Product',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Product for this line (if applicable)"
    )
    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        default=Decimal('1.000'),
        help_text="Quantity (for product lines)"
    )
    price_unit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Unit price (for product lines)"
    )

    # Reconciliation (Odoo concept for payments)
    reconciled = models.BooleanField(
        default=False,
        help_text="Whether this line is reconciled"
    )
    full_reconcile_id = models.ForeignKey(
        'AccountFullReconcile',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Full reconciliation this line belongs to"
    )

    # Company (multi-company)
    company_id = models.ForeignKey(
        'account.Company',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Company this line belongs to"
    )

    # System fields
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Account Move Line"
        verbose_name_plural = "Account Move Lines"
        ordering = ['move_id', 'id']

    def __str__(self):
        return f"{self.move_id.name} - {self.account_id.account_number}"

    def save(self, *args, **kwargs):
        """
        Odoo-style save with balance calculation
        """
        # Calculate balance (debit - credit)
        self.balance = self.debit - self.credit

        # Ensure only debit OR credit, not both (except for zero)
        if self.debit > 0 and self.credit > 0:
            raise ValueError("Line cannot have both debit and credit amounts")

        super().save(*args, **kwargs)


class AccountJournal(models.Model):
    """
    Odoo-style Account Journal

    Journals categorize accounting moves by type:
    - Sales Journal (customer invoices)
    - Purchase Journal (vendor bills)
    - Cash Journal (cash transactions)
    - Bank Journal (bank transactions)
    - Miscellaneous Journal (manual entries)
    """

    JOURNAL_TYPE_CHOICES = [
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('cash', 'Cash'),
        ('bank', 'Bank'),
        ('general', 'Miscellaneous'),
    ]

    # Basic information
    name = models.CharField(
        max_length=100,
        help_text="Journal name"
    )
    code = models.CharField(
        max_length=10,
        unique=True,
        help_text="Journal code (e.g., 'SAL', 'PUR')"
    )
    type = models.CharField(
        max_length=10,
        choices=JOURNAL_TYPE_CHOICES,
        help_text="Type of journal"
    )

    # Default accounts (Odoo approach)
    default_account_id = models.ForeignKey(
        Account,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="Default account for this journal"
    )

    # Sequence for numbering
    sequence_id = models.ForeignKey(
        IrSequence,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        help_text="Sequence for move numbering"
    )

    # Settings
    active = models.BooleanField(
        default=True,
        help_text="Whether this journal is active"
    )

    # Company
    company_id = models.ForeignKey(
        'account.Company',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Company this journal belongs to"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Account Journal"
        verbose_name_plural = "Account Journals"
        unique_together = [['code', 'company_id']]

    def __str__(self):
        return f"{self.code} - {self.name}"


class AccountFullReconcile(models.Model):
    """
    Odoo-style Full Reconciliation

    Groups account move lines that are fully reconciled
    (e.g., invoice + payment = reconciled)
    """

    name = models.CharField(
        max_length=100,
        help_text="Reconciliation reference"
    )

    # Company
    company_id = models.ForeignKey(
        'account.Company',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Company this reconciliation belongs to"
    )

    create_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Full Reconciliation"
        verbose_name_plural = "Full Reconciliations"

    def __str__(self):
        return self.name