#!/usr/bin/env python
import requests

auth_response = requests.post('http://localhost:8000/api-token-auth/', {'username': 'admin', 'password': 'admin123'})
token = auth_response.json()['token']
headers = {'Authorization': f'Token {token}'}

response = requests.get('http://localhost:8000/api/pricing/product-costs/all/', headers=headers)
print(f'Status: {response.status_code}')

if response.status_code == 200:
    data = response.json()
    print(f'Total products: {len(data)}')
else:
    print(f'Error: {response.text[:100]}')
