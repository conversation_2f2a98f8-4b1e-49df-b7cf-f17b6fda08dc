from django.contrib import admin
from django.forms import ModelForm, Select
from django.utils.html import format_html
from django.utils import timezone
from .models import (
    ProductCategory, Product, PaymentTerm, SalesOrder, SalesOrderLineItem,
    GoodsDeliveryNote, GoodsDeliveryNoteLineItem, CustomerInvoice, CustomerInvoiceLineItem,
    GoodsDeliveryReturnNote, GoodsDeliveryReturnNoteLineItem, CustomerBill, CustomerBillItem
)
from sales_tax.models import SalesTax


# Customer admin is now in contacts app - references moved to contacts.Contact

# Customer admin moved to contacts app - do not register here to avoid conflicts
# The CustomerAdmin class below is kept for reference only and is not registered
class CustomerAdminReference(admin.ModelAdmin):
    """
    DEPRECATED: Customer admin has been moved to contacts app.
    This is kept for reference but not registered to avoid conflicts.
    Use /admin/contacts/customer/ instead of /admin/sales/customer/
    
    Customer model is now accessed via contacts.Contact with contact_type='customer'
    """
    # This class is kept for documentation purposes only
    pass


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent_category', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent_category', 'created_at']
    search_fields = ['name', 'description']


class ProductAdminForm(ModelForm):
    """Custom form for Product admin with enhanced fields"""
    
    class Meta:
        model = Product
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add sales tax choices for tax rate field
        if 'sales_tax_category' in self.fields:
            output_taxes = SalesTax.objects.filter(tax_type='output').order_by('rate')
            choices = [('', 'No Tax')] + [(f"{tax.rate}%", f"{tax.description} ({tax.rate}%)") for tax in output_taxes]
            self.fields['sales_tax_category'].widget = Select(choices=choices)
            self.fields['sales_tax_category'].help_text = "Select output tax rate for sales"


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    form = ProductAdminForm
    list_display = [
        'name', 'sku', 'product_type', 'category', 
        'get_unit_price_display', 'get_cost_price_display', 'get_margin_display',
        'get_current_stock', 'get_income_account_display', 'status', 'created_at'
    ]
    list_filter = ['product_type', 'category', 'status', 'taxable', 'track_inventory', 'created_at']
    search_fields = [
        'name', 'sku', 'description', 
        'income_account_gl__account_name', 'expense_account_gl__account_name', 
        'inventory_asset_account_gl__account_name'
    ]
    readonly_fields = [
        'product_id', 'get_margin_display', 'get_markup_display', 
        'get_current_stock', 'price_last_updated_at', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('product_id', 'name', 'sku', 'product_type', 'category', 'description')
        }),
        ('💰 Sales Editable Fields', {
            'fields': ('unit_price', 'sales_tax_category'),
            'description': '🔹 Sales Department Access: You can only edit selling price and tax rates. Other product details are managed by the Purchase Department.',
        }),
        ('📊 Purchase Department Fields', {
            'fields': ('cost_price', 'minimum_selling_price', 'preferred_vendor'),
            'description': 'Managed by Purchase Department - Read Only for Sales',
        }),
        ('📈 Calculated Fields', {
            'fields': ('get_margin_display', 'get_markup_display'),
            'description': 'Auto-calculated based on cost and selling price',
        }),
        ('🏦 GL Account Integration', {
            'fields': ('income_account_gl', 'expense_account_gl', 'inventory_asset_account_gl'),
            'description': 'Link to Chart of Accounts for proper GL integration'
        }),
        ('📦 Inventory Information', {
            'fields': (
                'track_inventory', 'get_current_stock', 'reorder_point',
                'quantity_on_hand', 'quantity_on_purchase_order', 'quantity_on_sales_order'
            ),
            'description': 'Inventory tracking and stock levels'
        }),
        ('⚙️ Settings', {
            'fields': ('status', 'taxable')
        }),
        ('📅 Price History', {
            'fields': ('price_effective_date', 'price_last_updated_by', 'price_last_updated_at'),
        }),
        ('📝 Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by'),
        }),
    )
    
    def get_unit_price_display(self, obj):
        """Display unit price with USD currency symbol"""
        if obj.unit_price is not None:
            return format_html('<span style="color: green; font-weight: bold;">$ {}</span>', f'{float(obj.unit_price):,.2f}')
        return format_html('<span style="color: orange;">Not Set</span>')
    get_unit_price_display.short_description = "💰 Selling Price"
    get_unit_price_display.admin_order_field = 'unit_price'
    
    def get_cost_price_display(self, obj):
        """Display cost price with USD currency symbol"""
        if obj.cost_price is not None:
            return format_html('<span style="color: blue;">$ {}</span>', f'{float(obj.cost_price):,.2f}')
        return format_html('<span style="color: red;">Not Set</span>')
    get_cost_price_display.short_description = "📊 Cost Price"
    get_cost_price_display.admin_order_field = 'cost_price'
    
    def get_margin_display(self, obj):
        """Display margin amount and percentage"""
        if obj.cost_price and obj.cost_price > 0 and obj.unit_price is not None:
            margin_amount = float(obj.unit_price) - float(obj.cost_price)
            margin_percent = ((float(obj.unit_price) - float(obj.cost_price)) / float(obj.cost_price)) * 100
            color = 'green' if margin_amount > 0 else 'red'
            return format_html(
                '<span style="color: {}; font-weight: bold;">$ {} ({}%)</span>',
                color, f'{margin_amount:,.2f}', f'{margin_percent:.1f}'
            )
        return format_html('<span style="color: orange;">Cost price needed</span>')
    get_margin_display.short_description = "📈 Margin (Amount & %)"
    
    def get_markup_display(self, obj):
        """Display markup percentage"""
        if obj.cost_price and obj.cost_price > 0:
            markup = ((obj.unit_price - obj.cost_price) / obj.cost_price) * 100
            return f"{markup:.1f}%"
        return "Cost price needed"
    get_markup_display.short_description = "📊 Markup %"
    
    def get_current_stock(self, obj):
        """Display current stock with status indicator"""
        if obj.track_inventory:
            stock = obj.quantity_on_hand
            if stock <= 0:
                color = 'red'
                status = '⚠️ Out of Stock'
            elif stock <= obj.reorder_point:
                color = 'orange'
                status = '⚠️ Low Stock'
            else:
                color = 'green'
                status = '✅ In Stock'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{} units</span><br/><small>{}</small>',
                color, stock, status
            )
        return "Not tracked"
    get_current_stock.short_description = "📦 Current Stock"
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Customize dropdown options for GL accounts"""
        if db_field.name == "income_account_gl":
            # Only show Revenue accounts
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                account_type__type='REVENUE', 
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif db_field.name == "expense_account_gl":
            # Only show Expense accounts  
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                account_type__type='EXPENSE',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif db_field.name == "inventory_asset_account_gl":
            # Only show Asset accounts suitable for inventory
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                account_type__type='ASSET',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def get_income_account_display(self, obj):
        """Display income account with number and name"""
        if obj.income_account_gl:
            return f"{obj.income_account_gl.account_number} - {obj.income_account_gl.account_name}"
        return "Not Assigned"
    get_income_account_display.short_description = "Sales Account"
    
    def get_expense_account_display(self, obj):
        """Display expense account with number and name"""
        if obj.expense_account_gl:
            return f"{obj.expense_account_gl.account_number} - {obj.expense_account_gl.account_name}"
        return "Not Assigned"
    get_expense_account_display.short_description = "COGS Account"
    
    def get_inventory_account_display(self, obj):
        """Display inventory account with number and name"""
        if obj.inventory_asset_account_gl:
            return f"{obj.inventory_asset_account_gl.account_number} - {obj.inventory_asset_account_gl.account_name}"
        return "Not Assigned"
    get_inventory_account_display.short_description = "Inventory Account"
    
    def save_model(self, request, obj, form, change):
        """Override save to track price changes"""
        if change:
            # Check if unit_price changed
            try:
                original = Product.objects.get(pk=obj.pk)
                if original.unit_price != obj.unit_price:
                    obj.price_last_updated_by = request.user
                    obj.price_last_updated_at = timezone.now()
                    if not obj.price_effective_date:
                        obj.price_effective_date = timezone.now().date()
            except Product.DoesNotExist:
                pass
        
        super().save_model(request, obj, form, change)





@admin.register(PaymentTerm)
class PaymentTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'days', 'is_default', 'is_active', 'created_at']
    list_filter = ['is_default', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'days', 'description')
        }),
        ('Settings', {
            'fields': ('is_default', 'is_active')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


# Sales Workflow Admin Classes

class SalesOrderLineItemInline(admin.TabularInline):
    model = SalesOrderLineItem
    extra = 1
    fields = ['product', 'description', 'quantity', 'unit_price', 'discount_percent', 'line_total', 'tax_rate', 'tax_amount']
    readonly_fields = ['line_total', 'tax_amount']


@admin.register(SalesOrder)
class SalesOrderAdmin(admin.ModelAdmin):
    list_display = ['so_number', 'customer', 'so_date', 'expected_date', 'status', 'total_amount', 'balance_due', 'created_at']
    list_filter = ['status', 'so_date', 'created_at']
    search_fields = ['so_number', 'customer__name', 'seller_name', 'reference_number']
    readonly_fields = ['so_id', 'so_number', 'subtotal', 'total_amount', 'balance_due', 'created_at', 'updated_at']
    inlines = [SalesOrderLineItemInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('so_id', 'so_number', 'customer', 'so_date', 'expected_date', 'status')
        }),
        ('Seller Information', {
            'fields': ('seller_name', 'seller_email', 'seller_phone')
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'discount_percent', 'discount_amount', 'tax_amount', 'total_amount', 'amount_delivered', 'balance_due')
        }),
        ('Additional Information', {
            'fields': ('payment_terms', 'reference_number', 'memo', 'notes', 'ship_to_address')
        }),
        ('Email Tracking', {
            'fields': ('email_sent', 'email_sent_date')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


class GoodsDeliveryNoteLineItemInline(admin.TabularInline):
    model = GoodsDeliveryNoteLineItem
    extra = 0
    fields = ['product', 'description', 'quantity_ordered', 'quantity_delivered', 'quantity_remaining', 'unit_price']
    readonly_fields = ['quantity_remaining']


@admin.register(GoodsDeliveryNote)
class GoodsDeliveryNoteAdmin(admin.ModelAdmin):
    list_display = ['gdn_number', 'customer', 'sales_order', 'delivery_date', 'status', 'created_at']
    list_filter = ['status', 'delivery_date', 'created_at']
    search_fields = ['gdn_number', 'customer__name', 'sales_order__so_number']
    readonly_fields = ['gdn_id', 'gdn_number', 'created_at', 'updated_at']
    inlines = [GoodsDeliveryNoteLineItemInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('gdn_id', 'gdn_number', 'sales_order', 'customer', 'delivery_date', 'status')
        }),
        ('Delivery Details', {
            'fields': ('expected_delivery_date', 'actual_delivery_date', 'delivery_address', 'delivery_contact_person', 'delivery_contact_phone')
        }),
        ('Transport Information', {
            'fields': ('vehicle_number', 'driver_name', 'driver_phone')
        }),
        ('Confirmation', {
            'fields': ('customer_signature', 'received_by', 'received_date')
        }),
        ('Notes', {
            'fields': ('notes', 'internal_notes')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


class CustomerInvoiceLineItemInline(admin.TabularInline):
    model = CustomerInvoiceLineItem
    extra = 0
    fields = ['product', 'description', 'quantity', 'unit_price', 'line_total', 'tax_rate', 'tax_amount']
    readonly_fields = ['line_total', 'tax_amount']


@admin.register(CustomerInvoice)
class CustomerInvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'customer', 'invoice_type', 'invoice_date', 'due_date', 'status', 'total_amount', 'balance_due', 'created_at']
    list_filter = ['invoice_type', 'status', 'invoice_date', 'created_at']
    search_fields = ['invoice_number', 'customer__name', 'reference_number']
    readonly_fields = ['invoice_id', 'invoice_number', 'balance_due', 'created_at', 'updated_at']
    inlines = [CustomerInvoiceLineItemInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('invoice_id', 'invoice_number', 'customer', 'invoice_type', 'status')
        }),
        ('Source Information', {
            'fields': ('sales_order', 'delivery_note')
        }),
        ('Dates', {
            'fields': ('invoice_date', 'due_date', 'payment_date')
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'discount_percent', 'discount_amount', 'tax_amount', 'shipping_amount', 'total_amount', 'amount_paid', 'balance_due')
        }),
        ('Payment Information', {
            'fields': ('payment_terms', 'payment_method')
        }),
        ('Additional Information', {
            'fields': ('reference_number', 'memo', 'notes', 'billing_address')
        }),
        ('Email Tracking', {
            'fields': ('email_sent', 'email_sent_date', 'email_viewed', 'email_viewed_date')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


class GoodsDeliveryReturnNoteLineItemInline(admin.TabularInline):
    model = GoodsDeliveryReturnNoteLineItem
    extra = 0
    fields = ['product', 'description', 'quantity_delivered', 'quantity_returned', 'return_reason', 'condition']


@admin.register(GoodsDeliveryReturnNote)
class GoodsDeliveryReturnNoteAdmin(admin.ModelAdmin):
    list_display = ['gdrn_number', 'customer', 'delivery_note', 'return_date', 'return_reason', 'status', 'quality_check_passed', 'created_at']
    list_filter = ['return_reason', 'status', 'quality_check_passed', 'return_date', 'created_at']
    search_fields = ['gdrn_number', 'customer__name', 'delivery_note__gdn_number']
    readonly_fields = ['gdrn_id', 'gdrn_number', 'created_at', 'updated_at']
    inlines = [GoodsDeliveryReturnNoteLineItemInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('gdrn_id', 'gdrn_number', 'delivery_note', 'customer', 'return_date', 'status')
        }),
        ('Return Details', {
            'fields': ('return_reason', 'expected_return_date', 'actual_return_date', 'return_address', 'return_contact_person', 'return_contact_phone')
        }),
        ('Quality Control', {
            'fields': ('quality_check_passed', 'quality_check_notes', 'quality_checked_by', 'quality_check_date')
        }),
        ('Notes', {
            'fields': ('notes', 'internal_notes')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


class CustomerBillItemInline(admin.TabularInline):
    model = CustomerBillItem
    extra = 0
    fields = ['product', 'item_description', 'quantity', 'unit_price', 'line_total', 'tax_rate', 'tax_amount']
    readonly_fields = ['line_total', 'tax_amount']


@admin.register(CustomerBill)
class CustomerBillAdmin(admin.ModelAdmin):
    list_display = ['bill_number', 'customer', 'bill_type', 'bill_date', 'due_date', 'status', 'total_amount', 'balance_due', 'created_at']
    list_filter = ['bill_type', 'status', 'bill_date', 'created_at']
    search_fields = ['bill_number', 'customer__name', 'reference_number']
    readonly_fields = ['bill_id', 'bill_number', 'balance_due', 'created_at', 'updated_at']
    inlines = [CustomerBillItemInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('bill_id', 'bill_number', 'customer', 'bill_type', 'status')
        }),
        ('Source Information', {
            'fields': ('sales_order',)
        }),
        ('Dates', {
            'fields': ('bill_date', 'due_date')
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'tax_amount', 'discount_amount', 'shipping_amount', 'total_amount', 'amount_paid', 'balance_due')
        }),
        ('Payment Information', {
            'fields': ('payment_terms',)
        }),
        ('Additional Information', {
            'fields': ('reference_number', 'notes')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )

    def save_model(self, request, obj, form, change):
        """Override save to set created_by and handle GL entries"""
        if not change:  # New object
            obj.created_by = request.user

        # Save the object first
        super().save_model(request, obj, form, change)

        # If status is posted, create GL entries
        if obj.status == 'posted':
            try:
                obj.create_gl_entries()
                self.message_user(request, f"Customer bill {obj.bill_number} posted successfully. GL entries created.")
            except Exception as e:
                self.message_user(request, f"Error creating GL entries: {e}", level='ERROR')


@admin.register(CustomerBillItem)
class CustomerBillItemAdmin(admin.ModelAdmin):
    list_display = ['customer_bill', 'product', 'item_description', 'quantity', 'unit_price', 'line_total', 'tax_amount']
    list_filter = ['customer_bill__bill_type', 'customer_bill__status', 'product']
    search_fields = ['customer_bill__bill_number', 'product__name', 'item_description']
    readonly_fields = ['line_total', 'tax_amount']

    fieldsets = (
        ('Bill Information', {
            'fields': ('customer_bill',)
        }),
        ('Product Information', {
            'fields': ('product', 'item_description')
        }),
        ('Pricing', {
            'fields': ('quantity', 'unit_price', 'line_total')
        }),
        ('Tax Information', {
            'fields': ('taxable', 'tax_rate', 'tax_amount')
        }),
        ('GL Integration', {
            'fields': ('account_code', 'line_order')
        }),
    )

