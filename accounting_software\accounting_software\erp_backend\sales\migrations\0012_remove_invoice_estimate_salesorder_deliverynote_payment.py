# Generated manually for sales module cleanup
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0011_update_customer_references'),
    ]

    operations = [
        # Remove DeliveryNoteItem first (has foreign key to DeliveryNote)
        migrations.DeleteModel(
            name='DeliveryNoteItem',
        ),
        
        # Remove DeliveryNote
        migrations.DeleteModel(
            name='DeliveryNote',
        ),
        
        # Remove SalesOrderLineItem first (has foreign key to SalesOrder)
        migrations.DeleteModel(
            name='SalesOrderLineItem',
        ),
        
        # Remove SalesOrder
        migrations.DeleteModel(
            name='SalesOrder',
        ),
        
        # Remove InvoiceLineItem first (has foreign key to Invoice)
        migrations.DeleteModel(
            name='InvoiceLineItem',
        ),
        
        # Remove EstimateLineItem first (has foreign key to Estimate)
        migrations.DeleteModel(
            name='EstimateLineItem',
        ),
        
        # Remove Payment (has foreign key to Invoice)
        migrations.DeleteModel(
            name='Payment',
        ),
        
        # Remove Invoice
        migrations.DeleteModel(
            name='Invoice',
        ),
        
        # Remove Estimate
        migrations.DeleteModel(
            name='Estimate',
        ),
    ]
