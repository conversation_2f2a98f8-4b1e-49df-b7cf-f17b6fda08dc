# Generated by Django 4.2.21 on 2025-07-10 15:55

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Pricing', '0002_alter_pricelistitem_product'),
        ('contacts', '0006_remove_pricingrule_customer_and_more'),
        ('sales', '0012_remove_invoice_estimate_salesorder_deliverynote_payment'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('invoice_type', models.CharField(choices=[('invoice', 'Invoice'), ('credit', 'Credit Note')], default='invoice', max_length=20)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent'), ('viewed', 'Viewed'), ('partial', 'Partially Paid'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('invoice_date', models.DateField()),
                ('due_date', models.DateField()),
                ('payment_date', models.DateField(blank=True, null=True)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('shipping_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('balance_due', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('payment_terms', models.CharField(blank=True, max_length=20, null=True)),
                ('payment_method', models.CharField(blank=True, max_length=50, null=True)),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True)),
                ('memo', models.TextField(blank=True, help_text='Internal memo', null=True)),
                ('notes', models.TextField(blank=True, help_text='Notes to customer', null=True)),
                ('billing_address', models.TextField(blank=True, null=True)),
                ('email_sent', models.BooleanField(default=False)),
                ('email_sent_date', models.DateTimeField(blank=True, null=True)),
                ('email_viewed', models.BooleanField(default=False)),
                ('email_viewed_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_invoices_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_invoices', to='contacts.contact')),
            ],
            options={
                'verbose_name': 'Customer Invoice',
                'verbose_name_plural': 'Customer Invoices',
                'db_table': 'sales_customer_invoices',
                'ordering': ['-invoice_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoodsDeliveryNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gdn_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('gdn_number', models.CharField(max_length=50, unique=True)),
                ('delivery_date', models.DateField()),
                ('expected_delivery_date', models.DateField(blank=True, null=True)),
                ('actual_delivery_date', models.DateField(blank=True, null=True)),
                ('delivery_address', models.TextField(blank=True, null=True)),
                ('delivery_contact_person', models.CharField(blank=True, max_length=200, null=True)),
                ('delivery_contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('vehicle_number', models.CharField(blank=True, max_length=50, null=True)),
                ('driver_name', models.CharField(blank=True, max_length=200, null=True)),
                ('driver_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('confirmed', 'Confirmed'), ('delivered', 'Delivered'), ('returned', 'Returned'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('internal_notes', models.TextField(blank=True, null=True)),
                ('customer_signature', models.TextField(blank=True, help_text='Customer signature or confirmation', null=True)),
                ('received_by', models.CharField(blank=True, max_length=200, null=True)),
                ('received_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='delivery_notes_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='delivery_notes', to='contacts.contact')),
            ],
            options={
                'verbose_name': 'Goods Delivery Note',
                'verbose_name_plural': 'Goods Delivery Notes',
                'db_table': 'sales_goods_delivery_notes',
                'ordering': ['-delivery_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoodsDeliveryReturnNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gdrn_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('gdrn_number', models.CharField(max_length=50, unique=True)),
                ('return_date', models.DateField()),
                ('expected_return_date', models.DateField(blank=True, null=True)),
                ('actual_return_date', models.DateField(blank=True, null=True)),
                ('return_reason', models.CharField(choices=[('defective', 'Defective Product'), ('wrong_item', 'Wrong Item Delivered'), ('damaged', 'Damaged in Transit'), ('customer_request', 'Customer Request'), ('quality_issue', 'Quality Issue'), ('other', 'Other')], default='customer_request', max_length=50)),
                ('return_address', models.TextField(blank=True, null=True)),
                ('return_contact_person', models.CharField(blank=True, max_length=200, null=True)),
                ('return_contact_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('confirmed', 'Confirmed'), ('received', 'Received'), ('processed', 'Processed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('internal_notes', models.TextField(blank=True, null=True)),
                ('quality_check_passed', models.BooleanField(default=False)),
                ('quality_check_notes', models.TextField(blank=True, null=True)),
                ('quality_checked_by', models.CharField(blank=True, max_length=200, null=True)),
                ('quality_check_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='return_notes_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='return_notes', to='contacts.contact')),
                ('delivery_note', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='return_notes', to='sales.goodsdeliverynote')),
            ],
            options={
                'verbose_name': 'Goods Delivery Return Note',
                'verbose_name_plural': 'Goods Delivery Return Notes',
                'db_table': 'sales_goods_delivery_return_notes',
                'ordering': ['-return_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SalesOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('so_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('so_number', models.CharField(max_length=50, unique=True)),
                ('so_date', models.DateField()),
                ('expected_date', models.DateField(blank=True, null=True)),
                ('seller_name', models.CharField(blank=True, help_text='Name of the seller/salesperson', max_length=200, null=True)),
                ('seller_email', models.EmailField(blank=True, help_text='Email of the seller/salesperson', max_length=254, null=True)),
                ('seller_phone', models.CharField(blank=True, help_text='Phone of the seller/salesperson', max_length=20, null=True)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('amount_delivered', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('balance_due', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('sent', 'Sent'), ('acknowledged', 'Acknowledged'), ('partial', 'Partially Delivered'), ('delivered', 'Delivered'), ('closed', 'Closed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('payment_terms', models.CharField(blank=True, max_length=20, null=True)),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True)),
                ('memo', models.TextField(blank=True, help_text='Internal memo', null=True)),
                ('notes', models.TextField(blank=True, help_text='Notes to customer', null=True)),
                ('ship_to_address', models.TextField(blank=True, null=True)),
                ('email_sent', models.BooleanField(default=False)),
                ('email_sent_date', models.DateTimeField(blank=True, null=True)),
                ('acknowledged_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales_orders_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, help_text='Customer from contacts system', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales_orders', to='contacts.contact')),
            ],
            options={
                'db_table': 'sales_orders',
                'ordering': ['-so_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SalesOrderLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(help_text='Auto-filled from product, can be overridden')),
                ('quantity', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('unit_of_measure', models.CharField(default='pcs', help_text='Unit of measure (kg, L, pcs, etc.)', max_length=20)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, help_text='Sales price per unit', max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('line_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('taxable', models.BooleanField(default=True)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('quantity_delivered', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('quantity_pending', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('line_order', models.PositiveIntegerField(default=1, help_text='Order of line item in the sales order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(blank=True, help_text='Link to product from pricing module', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sales_line_items', to='Pricing.product')),
                ('sales_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.salesorder')),
            ],
            options={
                'db_table': 'sales_order_line_items',
                'ordering': ['line_order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoodsDeliveryNoteLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('quantity_ordered', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('quantity_delivered', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('quantity_remaining', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('unit_of_measure', models.CharField(default='pcs', max_length=20)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('line_order', models.PositiveIntegerField(default=1)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('delivery_note', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.goodsdeliverynote')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='delivery_line_items', to='Pricing.product')),
                ('sales_order_line_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_line_items', to='sales.salesorderlineitem')),
            ],
            options={
                'db_table': 'sales_goods_delivery_note_line_items',
                'ordering': ['line_order', 'created_at'],
                'unique_together': {('delivery_note', 'sales_order_line_item')},
            },
        ),
        migrations.AddField(
            model_name='goodsdeliverynote',
            name='sales_order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_notes', to='sales.salesorder'),
        ),
        migrations.CreateModel(
            name='CustomerInvoiceLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('quantity', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('unit_of_measure', models.CharField(default='pcs', max_length=20)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('line_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('account_code', models.CharField(blank=True, help_text='Revenue account code', max_length=20, null=True)),
                ('account_name', models.CharField(blank=True, help_text='Revenue account name', max_length=200, null=True)),
                ('line_order', models.PositiveIntegerField(default=1)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('delivery_note_line_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoice_line_items', to='sales.goodsdeliverynotelineitem')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.customerinvoice')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoice_line_items', to='Pricing.product')),
            ],
            options={
                'db_table': 'sales_customer_invoice_line_items',
                'ordering': ['line_order', 'created_at'],
            },
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='delivery_note',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoices', to='sales.goodsdeliverynote'),
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='sales_order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoices', to='sales.salesorder'),
        ),
        migrations.CreateModel(
            name='CustomerBill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bill_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('bill_number', models.CharField(help_text="Unique identifier (e.g., 'CBILL-2023-001')", max_length=50, unique=True)),
                ('bill_type', models.CharField(choices=[('bill', 'Bill'), ('credit', 'Credit')], default='bill', help_text='Type of bill - regular bill or credit', max_length=10)),
                ('bill_date', models.DateField(help_text='Date the bill was issued')),
                ('due_date', models.DateField(help_text='Date the bill is due for payment')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('balance_due', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('paid', 'Paid')], default='draft', max_length=20)),
                ('payment_terms', models.CharField(blank=True, max_length=100, null=True)),
                ('reference_number', models.CharField(blank=True, help_text="Customer's reference number", max_length=100, null=True)),
                ('notes', models.TextField(blank=True, help_text='Internal notes', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_bills_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, help_text='Link to customer master', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_bills', to='contacts.contact')),
                ('sales_order', models.ForeignKey(blank=True, help_text='Link to sales order if created from SO', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_bills', to='sales.salesorder')),
            ],
            options={
                'verbose_name': 'Customer Bill',
                'verbose_name_plural': 'Customer Bills',
                'db_table': 'sales_customer_bills',
                'ordering': ['-bill_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoodsDeliveryReturnNoteLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('quantity_delivered', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('quantity_returned', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('unit_of_measure', models.CharField(default='pcs', max_length=20)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('return_reason', models.CharField(blank=True, max_length=50, null=True)),
                ('condition', models.CharField(choices=[('good', 'Good Condition'), ('damaged', 'Damaged'), ('defective', 'Defective'), ('expired', 'Expired')], default='good', max_length=50)),
                ('line_order', models.PositiveIntegerField(default=1)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('delivery_note_line_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='return_line_items', to='sales.goodsdeliverynotelineitem')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='return_line_items', to='Pricing.product')),
                ('return_note', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.goodsdeliveryreturnnote')),
            ],
            options={
                'db_table': 'sales_goods_delivery_return_note_line_items',
                'ordering': ['line_order', 'created_at'],
                'unique_together': {('return_note', 'delivery_note_line_item')},
            },
        ),
        migrations.CreateModel(
            name='CustomerBillItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_description', models.TextField(help_text='Description of the item/service')),
                ('quantity', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, help_text='Sales price per unit', max_digits=15)),
                ('line_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('taxable', models.BooleanField(default=True)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('account_code', models.CharField(blank=True, help_text='Revenue account code for GL posting', max_length=50, null=True)),
                ('line_order', models.PositiveIntegerField(default=1, help_text='Order of line item in the bill')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer_bill', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.customerbill')),
                ('product', models.ForeignKey(blank=True, help_text='Link to product master for GL integration', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_bill_items', to='sales.product')),
            ],
            options={
                'db_table': 'sales_customer_bill_items',
                'ordering': ['line_order', 'created_at'],
                'unique_together': {('customer_bill', 'line_order')},
            },
        ),
    ]
