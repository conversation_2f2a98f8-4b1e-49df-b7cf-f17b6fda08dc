# Generated by Django 4.2.21 on 2025-07-11 03:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0013_add_sales_workflow_models'),
    ]

    operations = [
        migrations.AddField(
            model_name='customerbill',
            name='delivery_note',
            field=models.ForeignKey(blank=True, help_text='Linked delivery note for goods delivery bills', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_bills', to='sales.goodsdeliverynote'),
        ),
        migrations.AddField(
            model_name='customerbill',
            name='delivery_return_note',
            field=models.ForeignKey(blank=True, help_text='Linked delivery return note for return-based bills', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_bills', to='sales.goodsdeliveryreturnnote'),
        ),
        migrations.AddField(
            model_name='customerbill',
            name='source_type',
            field=models.CharField(choices=[('manual', 'Manual'), ('sales_order', 'Sales Order'), ('delivery_note', 'Delivery Note'), ('return_note', 'Return Note')], default='manual', help_text='Source document type for bill creation', max_length=20),
        ),
    ]
