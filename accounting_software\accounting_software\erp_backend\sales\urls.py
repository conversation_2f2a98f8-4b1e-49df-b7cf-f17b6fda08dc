from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

# Create a router and register our viewsets with it
router = DefaultRouter()
# Note: Customer endpoints moved to /api/contacts/customers/ - use contacts module instead
router.register(r'categories', views.ProductCategoryViewSet, basename='productcategory')
router.register(r'products', views.ProductViewSet, basename='product')
router.register(r'payment-terms', views.PaymentTermViewSet, basename='paymentterm')

# Sales Price Authority endpoint
router.register(r'product-pricing', views.ProductPricingViewSet, basename='productpricing')

# Sales Workflow endpoints
router.register(r'sales-orders', views.SalesOrderViewSet, basename='salesorder')
router.register(r'delivery-notes', views.GoodsDeliveryNoteViewSet, basename='goodsdeliverynote')
router.register(r'customer-invoices', views.CustomerInvoiceViewSet, basename='customerinvoice')
router.register(r'customer-bills', views.CustomerBillViewSet, basename='customerbill')
router.register(r'return-notes', views.GoodsDeliveryReturnNoteViewSet, basename='goodsdeliveryreturnnote')

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
] 