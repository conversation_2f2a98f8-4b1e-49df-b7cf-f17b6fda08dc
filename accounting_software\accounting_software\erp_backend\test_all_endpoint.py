#!/usr/bin/env python
import requests

def test_all_endpoint():
    print("=== TESTING /all/ ENDPOINT ===")
    
    auth_response = requests.post('http://localhost:8000/api-token-auth/', {'username': 'admin', 'password': 'admin123'})
    token = auth_response.json()['token']
    headers = {'Authorization': f'Token {token}'}
    
    # Test the new /all/ endpoint
    response = requests.get('http://localhost:8000/api/pricing/product-costs/all/', headers=headers)
    print(f'Status: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Response type: {type(data)}')
        if isinstance(data, list):
            print(f'Total products: {len(data)}')
            print('Sample products:')
            for i, product in enumerate(data[:5]):
                print(f'  {i+1}. {product.get("code")} - {product.get("name")}')
        else:
            print(f'Unexpected response format: {data}')
    else:
        print(f'Error: {response.text[:200]}')

if __name__ == '__main__':
    test_all_endpoint()
