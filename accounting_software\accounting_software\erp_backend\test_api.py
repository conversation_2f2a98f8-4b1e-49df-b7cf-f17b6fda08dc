#!/usr/bin/env python3
import requests
import json

# Test the customer bills API endpoints
base_url = "http://localhost:8000"

def test_endpoint(endpoint):
    try:
        print(f"\nTesting: {base_url}{endpoint}")
        response = requests.get(f"{base_url}{endpoint}", timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success!")
            print(f"Response: {json.dumps(data, indent=2)[:300]}...")
        elif response.status_code == 401:
            print(f"⚠️ Authentication required")
        elif response.status_code == 404:
            print(f"❌ Not Found")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"Exception: {e}")

# Test endpoints
endpoints = [
    "/api/sales/",
    "/api/sales/customer-bills/",
    "/api/sales/customer-bills/stats/",
]

for endpoint in endpoints:
    test_endpoint(endpoint)
