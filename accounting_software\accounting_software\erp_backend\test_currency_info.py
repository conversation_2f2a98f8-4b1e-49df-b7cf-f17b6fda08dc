#!/usr/bin/env python
"""
Test currency info endpoint
"""
import requests

def test_currency_info():
    print("=== TESTING CURRENCY INFO ENDPOINT ===")
    
    # Get authentication token
    auth_response = requests.post('http://localhost:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code != 200:
        print(f"❌ Authentication failed: {auth_response.status_code}")
        return
        
    token = auth_response.json().get('token')
    headers = {'Authorization': f'Token {token}'}
    
    # Test the currency info endpoint
    print("\n1. Testing GET /api/gl/company-currency-info/")
    response = requests.get('http://localhost:8000/api/gl/company-currency-info/', headers=headers)
    print(f'Status: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'✅ Success! Response: {data}')
    else:
        print(f'❌ Error: {response.text[:500]}')

if __name__ == '__main__':
    test_currency_info()
