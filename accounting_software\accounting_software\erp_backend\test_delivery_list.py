#!/usr/bin/env python
"""
Test delivery notes list endpoint
"""
import requests

def test_delivery_list():
    print("=== TESTING DELIVERY NOTES LIST ===")
    
    # Get authentication token
    auth_response = requests.post('http://localhost:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code != 200:
        print(f"❌ Authentication failed: {auth_response.status_code}")
        return
        
    token = auth_response.json().get('token')
    headers = {'Authorization': f'Token {token}'}
    
    # Test the exact endpoint that frontend calls
    print("\n1. Testing GET /api/sales/delivery-notes/ (list)")
    response = requests.get('http://localhost:8000/api/sales/delivery-notes/', headers=headers)
    print(f'Status: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'✅ Success! Response type: {type(data)}')
        if isinstance(data, dict):
            print(f'Keys: {list(data.keys())}')
            if 'results' in data:
                print(f'Results count: {len(data["results"])}')
        elif isinstance(data, list):
            print(f'Direct array length: {len(data)}')
    else:
        print(f'❌ Error: {response.text[:500]}')
    
    # Test with filters (like frontend might use)
    print("\n2. Testing with status filter")
    response = requests.get('http://localhost:8000/api/sales/delivery-notes/', 
                           headers=headers, 
                           params={'status': 'draft'})
    print(f'Status: {response.status_code}')
    
    if response.status_code == 200:
        print('✅ Success with filter')
    else:
        print(f'❌ Error with filter: {response.text[:200]}')

if __name__ == '__main__':
    test_delivery_list()
