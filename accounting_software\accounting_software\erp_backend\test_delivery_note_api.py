#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from sales.models import GoodsDeliveryNote, SalesOrder
from contacts.models import Contact
from datetime import date

def test_model_creation():
    """Test creating a delivery note directly in Django"""
    print("=== Testing Model Creation ===")
    try:
        # Get a sales order
        so = SalesOrder.objects.first()
        print(f"Sales Order: {so}")
        
        if not so:
            print("No sales orders found. Creating one...")
            # Create a simple sales order for testing
            customer = Contact.objects.filter(contact_type='customer').first()
            if not customer:
                print("No customers found. Please create a customer first.")
                return
                
            so = SalesOrder.objects.create(
                customer=customer,
                so_date=date.today(),
                expected_date=date.today(),
                status='pending',
                total_amount=100.00
            )
            print(f"Created Sales Order: {so}")
        
        # Create delivery note data
        gdn_data = {
            'sales_order': so,
            'delivery_date': date.today(),
            'delivery_address': 'Test Address',
            'status': 'draft'
        }
        
        # Try to create
        gdn = GoodsDeliveryNote(**gdn_data)
        gdn.save()
        print(f"✅ Created GDN: {gdn}")
        
        # Clean up
        gdn.delete()
        print("✅ Model creation test passed")
        
    except Exception as e:
        print(f"❌ Error in model creation: {e}")
        import traceback
        traceback.print_exc()

def test_api_creation():
    """Test creating a delivery note via API"""
    print("\n=== Testing API Creation ===")
    try:
        # Get auth token
        auth_response = requests.post('http://localhost:8000/api-token-auth/', {
            'username': 'admin',
            'password': 'admin123'
        })
        
        if auth_response.status_code != 200:
            print(f"❌ Auth failed: {auth_response.status_code}")
            print(auth_response.text)
            return
            
        token = auth_response.json().get('token')
        headers = {'Authorization': f'Token {token}', 'Content-Type': 'application/json'}
        
        # Get a sales order
        so_response = requests.get('http://localhost:8000/api/sales/sales-orders/', headers=headers)
        if so_response.status_code != 200:
            print(f"❌ Failed to get sales orders: {so_response.status_code}")
            return
            
        sales_orders = so_response.json().get('results', [])
        if not sales_orders:
            print("❌ No sales orders found")
            return
            
        so = sales_orders[0]
        print(f"Using Sales Order: {so['id']} - {so.get('so_number', 'N/A')}")
        
        # Create delivery note - match frontend data structure
        from datetime import timedelta
        delivery_note_data = {
            'sales_order': so['id'],
            'customer': so.get('customer_id') or so.get('customer'),
            'delivery_date': str(date.today()),
            'expected_delivery_date': str(date.today() + timedelta(days=1)),
            'delivery_address': 'Test Address via API',
            'delivery_contact_person': '',
            'delivery_contact_phone': '',
            'vehicle_number': '',
            'driver_name': '',
            'driver_phone': '',
            'notes': '',
            'internal_notes': '',
            'status': 'draft'
        }
        
        print(f"Sending data: {json.dumps(delivery_note_data, indent=2)}")
        
        response = requests.post(
            'http://localhost:8000/api/sales/delivery-notes/',
            headers=headers,
            json=delivery_note_data
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response content: {response.text[:1000]}...")
        
        if response.status_code == 201:
            print("✅ API creation test passed")
        else:
            print(f"❌ API creation failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error in API creation: {e}")
        import traceback
        traceback.print_exc()

def test_frontend_simulation():
    """Test simulating exact frontend request"""
    print("\n=== Testing Frontend Simulation ===")
    try:
        # Get auth token
        auth_response = requests.post('http://localhost:8000/api-token-auth/', {
            'username': 'admin',
            'password': 'admin123'
        })

        if auth_response.status_code != 200:
            print(f"❌ Auth failed: {auth_response.status_code}")
            return

        token = auth_response.json().get('token')

        # Simulate exact frontend headers and request
        headers = {
            'Authorization': f'Token {token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Origin': 'http://localhost:5173',
            'Referer': 'http://localhost:5173/'
        }

        # Get a sales order first
        so_response = requests.get('http://localhost:8000/api/sales/sales-orders/', headers=headers)
        if so_response.status_code != 200:
            print(f"❌ Failed to get sales orders: {so_response.status_code}")
            return

        sales_orders = so_response.json().get('results', [])
        if not sales_orders:
            print("❌ No sales orders found")
            return

        so = sales_orders[0]

        # Create delivery note with exact frontend data structure
        from datetime import timedelta
        delivery_note_data = {
            'sales_order': so['id'],
            'customer': so.get('customer_id') or so.get('customer'),
            'delivery_date': str(date.today()),
            'expected_delivery_date': str(date.today() + timedelta(days=1)),
            'delivery_address': 'Test Address Frontend Simulation',
            'delivery_contact_person': '',
            'delivery_contact_phone': '',
            'vehicle_number': '',
            'driver_name': '',
            'driver_phone': '',
            'notes': '',
            'internal_notes': '',
            'status': 'draft'
        }

        print(f"Frontend simulation data: {json.dumps(delivery_note_data, indent=2)}")

        response = requests.post(
            'http://localhost:8000/api/sales/delivery-notes/',
            headers=headers,
            json=delivery_note_data
        )

        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text[:500]}...")

        if response.status_code == 201:
            print("✅ Frontend simulation test passed")
        else:
            print(f"❌ Frontend simulation failed: {response.status_code}")

    except Exception as e:
        print(f"❌ Error in frontend simulation: {e}")
        import traceback
        traceback.print_exc()

def test_with_invalid_token():
    """Test with invalid token to see error handling"""
    print("\n=== Testing Invalid Token ===")
    try:
        headers = {
            'Authorization': 'Token invalid_token_12345',
            'Content-Type': 'application/json'
        }

        delivery_note_data = {
            'sales_order': 14,
            'delivery_date': str(date.today()),
            'delivery_address': 'Test Address',
            'status': 'draft'
        }

        response = requests.post(
            'http://localhost:8000/api/sales/delivery-notes/',
            headers=headers,
            json=delivery_note_data
        )

        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text[:500]}...")

        if response.status_code == 401:
            print("✅ Invalid token correctly returns 401")
        else:
            print(f"❌ Unexpected status code: {response.status_code}")

    except Exception as e:
        print(f"❌ Error testing invalid token: {e}")

def test_with_no_token():
    """Test with no token to see error handling"""
    print("\n=== Testing No Token ===")
    try:
        headers = {
            'Content-Type': 'application/json'
        }

        delivery_note_data = {
            'sales_order': 14,
            'delivery_date': str(date.today()),
            'delivery_address': 'Test Address',
            'status': 'draft'
        }

        response = requests.post(
            'http://localhost:8000/api/sales/delivery-notes/',
            headers=headers,
            json=delivery_note_data
        )

        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text[:500]}...")

        if response.status_code == 401:
            print("✅ No token correctly returns 401")
        else:
            print(f"❌ Unexpected status code: {response.status_code}")

    except Exception as e:
        print(f"❌ Error testing no token: {e}")

if __name__ == '__main__':
    test_model_creation()
    test_api_creation()
    test_frontend_simulation()
    test_with_invalid_token()
    test_with_no_token()
