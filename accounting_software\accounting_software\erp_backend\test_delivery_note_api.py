#!/usr/bin/env python
import os
import sys
import django
import requests
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from sales.models import GoodsDeliveryNote, SalesOrder
from contacts.models import Contact
from datetime import date

def test_model_creation():
    """Test creating a delivery note directly in Django"""
    print("=== Testing Model Creation ===")
    try:
        # Get a sales order
        so = SalesOrder.objects.first()
        print(f"Sales Order: {so}")
        
        if not so:
            print("No sales orders found. Creating one...")
            # Create a simple sales order for testing
            customer = Contact.objects.filter(contact_type='customer').first()
            if not customer:
                print("No customers found. Please create a customer first.")
                return
                
            so = SalesOrder.objects.create(
                customer=customer,
                so_date=date.today(),
                expected_date=date.today(),
                status='pending',
                total_amount=100.00
            )
            print(f"Created Sales Order: {so}")
        
        # Create delivery note data
        gdn_data = {
            'sales_order': so,
            'delivery_date': date.today(),
            'delivery_address': 'Test Address',
            'status': 'draft'
        }
        
        # Try to create
        gdn = GoodsDeliveryNote(**gdn_data)
        gdn.save()
        print(f"✅ Created GDN: {gdn}")
        
        # Clean up
        gdn.delete()
        print("✅ Model creation test passed")
        
    except Exception as e:
        print(f"❌ Error in model creation: {e}")
        import traceback
        traceback.print_exc()

def test_api_creation():
    """Test creating a delivery note via API"""
    print("\n=== Testing API Creation ===")
    try:
        # Get auth token
        auth_response = requests.post('http://localhost:8000/api-token-auth/', {
            'username': 'admin',
            'password': 'admin123'
        })
        
        if auth_response.status_code != 200:
            print(f"❌ Auth failed: {auth_response.status_code}")
            print(auth_response.text)
            return
            
        token = auth_response.json().get('token')
        headers = {'Authorization': f'Token {token}', 'Content-Type': 'application/json'}
        
        # Get a sales order
        so_response = requests.get('http://localhost:8000/api/sales/sales-orders/', headers=headers)
        if so_response.status_code != 200:
            print(f"❌ Failed to get sales orders: {so_response.status_code}")
            return
            
        sales_orders = so_response.json().get('results', [])
        if not sales_orders:
            print("❌ No sales orders found")
            return
            
        so = sales_orders[0]
        print(f"Using Sales Order: {so['id']} - {so.get('so_number', 'N/A')}")
        
        # Create delivery note
        delivery_note_data = {
            'sales_order': so['id'],
            'delivery_date': str(date.today()),
            'delivery_address': 'Test Address via API',
            'status': 'draft'
        }
        
        print(f"Sending data: {json.dumps(delivery_note_data, indent=2)}")
        
        response = requests.post(
            'http://localhost:8000/api/sales/delivery-notes/',
            headers=headers,
            json=delivery_note_data
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response content: {response.text[:1000]}...")
        
        if response.status_code == 201:
            print("✅ API creation test passed")
        else:
            print(f"❌ API creation failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error in API creation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_model_creation()
    test_api_creation()
