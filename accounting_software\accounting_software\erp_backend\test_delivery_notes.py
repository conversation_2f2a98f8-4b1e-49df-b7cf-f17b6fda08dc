#!/usr/bin/env python
"""
Test delivery notes endpoint to find the 500 error
"""
import requests

def test_delivery_notes():
    print("=== TESTING DELIVERY NOTES ENDPOINT ===")
    
    # Get authentication token
    auth_response = requests.post('http://localhost:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code != 200:
        print(f"❌ Authentication failed: {auth_response.status_code}")
        return
        
    token = auth_response.json().get('token')
    headers = {'Authorization': f'Token {token}'}
    
    # Test GET request to delivery notes
    print("\n1. Testing GET /api/sales/delivery-notes/")
    response = requests.get('http://localhost:8000/api/sales/delivery-notes/', headers=headers)
    print(f'Status: {response.status_code}')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Success! Found {len(data.get("results", data))} delivery notes')
    else:
        print(f'Error: {response.text[:500]}')
    
    # Test POST request (create delivery note)
    print("\n2. Testing POST /api/sales/delivery-notes/")
    test_data = {
        "sales_order": 1,  # Assuming sales order with ID 1 exists
        "delivery_date": "2024-01-15",
        "status": "draft",
        "notes": "Test delivery note"
    }
    
    response = requests.post('http://localhost:8000/api/sales/delivery-notes/', 
                           headers=headers, 
                           json=test_data)
    print(f'Status: {response.status_code}')
    
    if response.status_code in [200, 201]:
        print('Success! Delivery note created')
    else:
        print(f'Error: {response.text[:500]}')

if __name__ == '__main__':
    test_delivery_notes()
