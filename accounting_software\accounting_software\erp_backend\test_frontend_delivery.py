#!/usr/bin/env python
"""
Test delivery notes with frontend-like data
"""
import requests
import json

def test_frontend_delivery():
    print("=== TESTING FRONTEND-LIKE DELIVERY NOTE ===")
    
    # Get authentication token
    auth_response = requests.post('http://localhost:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code != 200:
        print(f"❌ Authentication failed: {auth_response.status_code}")
        return
        
    token = auth_response.json().get('token')
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    # Test data that mimics what frontend sends (with null values)
    frontend_data = {
        "sales_order": None,  # This might be the issue
        "customer": None,
        "delivery_date": "2024-01-15",
        "expected_delivery_date": "2024-01-16",
        "delivery_address": "Test Address",
        "delivery_contact_person": "Test Person",
        "delivery_contact_phone": "123456789",
        "vehicle_number": "ABC123",
        "driver_name": "Test Driver",
        "driver_phone": "987654321",
        "notes": "Test notes",
        "internal_notes": "Internal test notes",
        "status": "draft"
    }
    
    print("\n1. Testing with null sales_order (like frontend might send)")
    response = requests.post('http://localhost:8000/api/sales/delivery-notes/', 
                           headers=headers, 
                           json=frontend_data)
    print(f'Status: {response.status_code}')
    
    if response.status_code in [200, 201]:
        print('✅ Success with null sales_order')
    else:
        print(f'❌ Error with null sales_order: {response.text[:500]}')
    
    # Test with valid sales_order
    print("\n2. Testing with valid sales_order")
    frontend_data["sales_order"] = 1  # Use a valid sales order ID
    
    response = requests.post('http://localhost:8000/api/sales/delivery-notes/', 
                           headers=headers, 
                           json=frontend_data)
    print(f'Status: {response.status_code}')
    
    if response.status_code in [200, 201]:
        print('✅ Success with valid sales_order')
    else:
        print(f'❌ Error with valid sales_order: {response.text[:500]}')

if __name__ == '__main__':
    test_frontend_delivery()
