#!/usr/bin/env python
"""
Test script to simulate what the frontend PriceListItemManager is doing
"""
import os
import sys
import django
import requests

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

def test_frontend_pricing_flow():
    """Test the exact flow that PriceListItemManager uses"""
    print("=== TESTING FRONTEND PRICING FLOW ===\n")
    
    # Get authentication token
    auth_response = requests.post('http://localhost:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code != 200:
        print(f"❌ Authentication failed: {auth_response.status_code}")
        return
        
    token = auth_response.json().get('token')
    headers = {'Authorization': f'Token {token}'}
    print(f"✅ Authentication successful")
    
    # Step 1: Fetch data exactly like PriceListItemManager does
    print("\n1. Fetching data like PriceListItemManager...")
    
    try:
        # Simulate the Promise.all call
        print("   - Fetching price lists...")
        price_lists_response = requests.get(
            'http://localhost:8000/api/pricing/price-lists/',
            headers=headers,
            params={'page_size': 1000}
        )
        
        print("   - Fetching products...")
        products_response = requests.get(
            'http://localhost:8000/api/pricing/product-costs/',
            headers=headers
        )
        
        print("   - Fetching price list items...")
        items_response = requests.get(
            'http://localhost:8000/api/pricing/price-list-items/',
            headers=headers,
            params={'page_size': 1000}
        )
        
        # Check responses
        print(f"\n2. Response Status Codes:")
        print(f"   Price Lists: {price_lists_response.status_code}")
        print(f"   Products: {products_response.status_code}")
        print(f"   Price List Items: {items_response.status_code}")
        
        if all(r.status_code == 200 for r in [price_lists_response, products_response, items_response]):
            print("✅ All API calls successful")
            
            # Parse data like frontend does
            price_lists_data = price_lists_response.json()
            products_data = products_response.json()
            items_data = items_response.json()
            
            # Handle pagination like frontend
            final_price_lists = price_lists_data.get('results', price_lists_data) if isinstance(price_lists_data, dict) else price_lists_data
            final_products = products_data.get('results', products_data) if isinstance(products_data, dict) else products_data
            final_items = items_data.get('results', items_data) if isinstance(items_data, dict) else items_data
            
            print(f"\n3. Data Counts:")
            print(f"   Price Lists: {len(final_price_lists) if isinstance(final_price_lists, list) else 'Not a list'}")
            print(f"   Products: {len(final_products) if isinstance(final_products, list) else 'Not a list'}")
            print(f"   Price List Items: {len(final_items) if isinstance(final_items, list) else 'Not a list'}")
            
            # Show sample products for dropdown
            if isinstance(final_products, list) and final_products:
                print(f"\n4. Sample Products for Dropdown:")
                for i, product in enumerate(final_products[:3]):
                    code = product.get('code', 'No code')
                    name = product.get('name', 'No name')
                    display_text = f"{code} - {name}" if code != 'No code' else name
                    print(f"   {i+1}. ID: {product.get('id')} | Display: {display_text}")
                    
                print(f"\n✅ Products dropdown should show {len(final_products)} items!")
            else:
                print(f"\n❌ No products found for dropdown")
                print(f"   Products data type: {type(final_products)}")
                print(f"   Products data: {final_products}")
                
        else:
            print("❌ Some API calls failed")
            for name, response in [('Price Lists', price_lists_response), ('Products', products_response), ('Items', items_response)]:
                if response.status_code != 200:
                    print(f"   {name}: {response.status_code} - {response.text[:100]}")
                    
    except Exception as e:
        print(f"❌ Error during API calls: {e}")

if __name__ == '__main__':
    test_frontend_pricing_flow()
