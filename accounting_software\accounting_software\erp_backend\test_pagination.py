#!/usr/bin/env python
"""
Test pagination in product API
"""
import requests

def test_pagination():
    print("=== TESTING PRODUCT API PAGINATION ===")
    
    # Get authentication token
    auth_response = requests.post('http://localhost:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code != 200:
        print(f"❌ Authentication failed: {auth_response.status_code}")
        return
        
    token = auth_response.json().get('token')
    headers = {'Authorization': f'Token {token}'}
    
    # Test product endpoint with different page sizes
    endpoints_to_test = [
        '/api/pricing/product-costs/',
        '/api/pricing/product-costs/?page_size=100',
        '/api/pricing/product-costs/?page_size=1000',
    ]
    
    for endpoint in endpoints_to_test:
        print(f"\n--- Testing {endpoint} ---")
        response = requests.get(f'http://localhost:8000{endpoint}', headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response type: {type(data)}")
            
            if isinstance(data, dict):
                print(f"Response keys: {list(data.keys())}")
                if 'results' in data:
                    print(f"Results count: {len(data['results'])}")
                    print(f"Count field: {data.get('count', 'Not present')}")
                    print(f"Next page: {data.get('next', 'None')}")
                    print(f"Previous page: {data.get('previous', 'None')}")
                else:
                    print(f"Direct array length: {len(data)}")
            elif isinstance(data, list):
                print(f"Direct array length: {len(data)}")
        else:
            print(f"Error: {response.status_code} - {response.text[:100]}")

if __name__ == '__main__':
    test_pagination()
