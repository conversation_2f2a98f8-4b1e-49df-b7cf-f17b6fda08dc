#!/usr/bin/env python
"""
Test script to verify Pricing module functionality
"""
import os
import sys
import django
import requests
import json
from datetime import date

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from Pricing.models import Product, PriceList, PriceListItem

def test_pricing_module():
    """Test the complete Pricing module functionality"""
    print("=== TESTING PRICING MODULE ===\n")
    
    # Step 1: Get authentication token
    print("1. Getting authentication token...")
    auth_response = requests.post('http://localhost:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code != 200:
        print(f"❌ Authentication failed: {auth_response.status_code}")
        return
        
    token = auth_response.json().get('token')
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    print(f"✅ Authentication successful")
    
    # Step 2: Create test products via API
    print("\n2. Creating test products via API...")
    products_data = [
        {
            'code': 'LAP001',
            'name': 'Laptop Computer',
            'description': 'High-performance laptop',
            'type': 'product',
            'uom': 'each',
            'standard_cost': 800.00
        },
        {
            'code': 'MOU001', 
            'name': 'Wireless Mouse',
            'description': 'Ergonomic wireless mouse',
            'type': 'product',
            'uom': 'each',
            'standard_cost': 15.00
        }
    ]
    
    created_products = []
    for product_data in products_data:
        response = requests.post(
            'http://localhost:8000/api/pricing/products/',
            headers=headers,
            json=product_data
        )
        
        if response.status_code == 201:
            product = response.json()
            created_products.append(product)
            print(f"✅ Created product: {product['name']} ({product['code']})")
        else:
            print(f"❌ Failed to create product {product_data['name']}: {response.status_code}")
            print(f"   Response: {response.text}")
    
    # Step 3: Create price list via API
    print("\n3. Creating price list via API...")
    price_list_data = {
        'name': 'Standard Price List',
        'description': 'Default pricing for all customers',
        'currency': 'USD',
        'is_default': True,
        'is_active': True
    }
    
    response = requests.post(
        'http://localhost:8000/api/pricing/price-lists/',
        headers=headers,
        json=price_list_data
    )
    
    if response.status_code == 201:
        price_list = response.json()
        print(f"✅ Created price list: {price_list['name']}")
    else:
        print(f"❌ Failed to create price list: {response.status_code}")
        print(f"   Response: {response.text}")
        return
    
    # Step 4: Create price list items via API
    print("\n4. Creating price list items via API...")
    pricing_data = [
        {'product_id': created_products[0]['id'], 'unit_price': 1200.00},
        {'product_id': created_products[1]['id'], 'unit_price': 25.00},
    ]
    
    for price_data in pricing_data:
        price_item_data = {
            'price_list': price_list['id'],
            'product': price_data['product_id'],
            'unit_price': price_data['unit_price'],
            'min_quantity': 1
        }
        
        response = requests.post(
            'http://localhost:8000/api/pricing/price-list-items/',
            headers=headers,
            json=price_item_data
        )
        
        if response.status_code == 201:
            item = response.json()
            print(f"✅ Created price item: {item['product_name']} @ ${item['unit_price']}")
        else:
            print(f"❌ Failed to create price item: {response.status_code}")
            print(f"   Response: {response.text}")
    
    # Step 5: Test pricing API
    print("\n5. Testing pricing API...")
    for product in created_products:
        response = requests.get(
            f"http://localhost:8000/api/pricing/pricing/get_price/?product={product['id']}&quantity=1",
            headers=headers
        )
        
        if response.status_code == 200:
            price_info = response.json()
            print(f"✅ Price for {product['name']}: ${price_info['unit_price']}")
            print(f"   Cost: ${price_info['cost_price']}, Margin: {price_info['margin_percent']:.1f}%")
        else:
            print(f"❌ Failed to get price for {product['name']}: {response.status_code}")
    
    # Step 6: Test API endpoints
    print("\n6. Testing API endpoints...")
    endpoints = [
        ('GET', '/api/pricing/products/', 'Products list'),
        ('GET', '/api/pricing/price-lists/', 'Price lists'),
        ('GET', '/api/pricing/price-list-items/', 'Price list items'),
        ('GET', '/api/pricing/product-costs/', 'Product costs'),
    ]
    
    for method, endpoint, description in endpoints:
        response = requests.get(f'http://localhost:8000{endpoint}', headers=headers)
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('results', data)) if isinstance(data, dict) and 'results' in data else len(data)
            print(f"✅ {description}: {count} items")
        else:
            print(f"❌ {description} failed: {response.status_code}")
    
    # Step 7: Summary
    print(f"\n=== PRICING MODULE TEST SUMMARY ===")
    print(f"✅ Products created: {len(created_products)}")
    print(f"✅ Price list created: {price_list['name']}")
    print(f"✅ API endpoints working")
    print(f"\n🎉 PRICING MODULE IS WORKING!")

if __name__ == '__main__':
    test_pricing_module()
