#!/usr/bin/env python
"""
Simple test script to verify Pricing module functionality using Django shell
"""
import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from Pricing.models import Product, PriceList, PriceListItem
from Pricing.services import PricingService

def test_pricing_models():
    """Test Pricing models and services"""
    print("=== TESTING PRICING MODULE (Django Shell) ===\n")
    
    # Step 1: Create test products
    print("1. Creating test products...")
    products_data = [
        {
            'code': 'LAP001',
            'name': 'Laptop Computer',
            'description': 'High-performance laptop',
            'type': 'product',
            'uom': 'each',
            'standard_cost': 800.00
        },
        {
            'code': 'MOU001', 
            'name': 'Wireless Mouse',
            'description': 'Ergonomic wireless mouse',
            'type': 'product',
            'uom': 'each',
            'standard_cost': 15.00
        }
    ]
    
    created_products = []
    for product_data in products_data:
        product, created = Product.objects.get_or_create(
            code=product_data['code'],
            defaults=product_data
        )
        if created:
            print(f"✅ Created product: {product.name} ({product.code})")
        else:
            print(f"📦 Product exists: {product.name} ({product.code})")
        created_products.append(product)
    
    # Step 2: Create price list
    print("\n2. Creating price list...")
    price_list, created = PriceList.objects.get_or_create(
        name='Standard Price List',
        defaults={
            'description': 'Default pricing for all customers',
            'currency': 'USD',
            'is_default': True,
            'is_active': True
        }
    )
    if created:
        print(f"✅ Created price list: {price_list.name}")
    else:
        print(f"💰 Price list exists: {price_list.name}")
    
    # Step 3: Create price list items
    print("\n3. Creating price list items...")
    pricing_data = [
        {'product': created_products[0], 'unit_price': 1200.00},
        {'product': created_products[1], 'unit_price': 25.00},
    ]
    
    for price_data in pricing_data:
        price_item, created = PriceListItem.objects.get_or_create(
            price_list=price_list,
            product=price_data['product'],
            defaults={
                'unit_price': price_data['unit_price'],
                'min_quantity': 1
            }
        )
        if created:
            print(f"✅ Created price item: {price_data['product'].name} @ ${price_data['unit_price']}")
        else:
            print(f"💰 Price item exists: {price_data['product'].name} @ ${price_item.unit_price}")
    
    # Step 4: Test pricing service
    print("\n4. Testing pricing service...")
    for product in created_products:
        price = PricingService.get_product_price(product=product, quantity=1)
        if price:
            print(f"✅ Price for {product.name}: ${price}")
            print(f"   Cost: ${product.standard_cost}, Margin: ${price - product.standard_cost}")
        else:
            print(f"❌ No price found for {product.name}")
    
    # Step 5: Test model counts
    print("\n5. Checking data counts...")
    print(f"   Products: {Product.objects.count()}")
    print(f"   Price Lists: {PriceList.objects.count()}")
    print(f"   Price List Items: {PriceListItem.objects.count()}")
    
    # Step 6: Test model relationships
    print("\n6. Testing model relationships...")
    for product in created_products:
        price_items = PriceListItem.objects.filter(product=product)
        print(f"   {product.name} has {price_items.count()} price items")
        for item in price_items:
            print(f"     - {item.price_list.name}: ${item.unit_price}")
    
    print(f"\n=== PRICING MODULE TEST SUMMARY ===")
    print(f"✅ Products: {len(created_products)}")
    print(f"✅ Price list: {price_list.name}")
    print(f"✅ Price items: {PriceListItem.objects.count()}")
    print(f"✅ Pricing service working")
    print(f"\n🎉 PRICING MODULE IS WORKING!")

if __name__ == '__main__':
    test_pricing_models()
