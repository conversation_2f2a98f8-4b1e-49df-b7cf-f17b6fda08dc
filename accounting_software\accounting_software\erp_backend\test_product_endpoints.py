#!/usr/bin/env python
"""
Test script to check product endpoints for the pricing module
"""
import os
import sys
import django
import requests

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

def test_product_endpoints():
    """Test all product endpoints that the frontend might use"""
    print("=== TESTING PRODUCT ENDPOINTS ===\n")
    
    # Get authentication token
    auth_response = requests.post('http://localhost:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code != 200:
        print(f"❌ Authentication failed: {auth_response.status_code}")
        return
        
    token = auth_response.json().get('token')
    headers = {'Authorization': f'Token {token}'}
    print(f"✅ Authentication successful")
    
    # Test the endpoints that productService tries
    endpoints = [
        '/api/pricing/product-costs/',  # This is what pricingService.getProductCosts() calls
        '/api/sales/products/',
        '/api/gl/products/',
        '/api/pricing/products/'
    ]
    
    for endpoint in endpoints:
        print(f"\n--- Testing {endpoint} ---")
        try:
            response = requests.get(f'http://localhost:8000{endpoint}', headers=headers)
            print(f'Status: {response.status_code}')
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and 'results' in data:
                    print(f'Items: {len(data["results"])}')
                    if data['results']:
                        sample = data['results'][0]
                        print(f'Sample item fields: {list(sample.keys())}')
                        print(f'Sample name: {sample.get("name", "No name field")}')
                        print(f'Sample code: {sample.get("code", sample.get("sku", "No code/sku field"))}')
                elif isinstance(data, list):
                    print(f'Items: {len(data)}')
                    if data:
                        sample = data[0]
                        print(f'Sample item fields: {list(sample.keys())}')
                        print(f'Sample name: {sample.get("name", "No name field")}')
                else:
                    print(f'Data type: {type(data)}')
                    print(f'Data: {data}')
            else:
                print(f'Error: {response.text[:200]}')
        except Exception as e:
            print(f'Exception: {e}')

if __name__ == '__main__':
    test_product_endpoints()
