#!/usr/bin/env python
"""
Test the SalesProductSerializer
"""
import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from sales.models import Product
from Pricing.serializers import SalesProductSerializer

def test_serializer():
    print("=== TESTING SALES PRODUCT SERIALIZER ===")
    
    # Test the serializer with a sample product
    product = Product.objects.first()
    if product:
        print(f'Testing product: {product.name}')
        print(f'Product fields: sku={product.sku}, name={product.name}, product_type={product.product_type}, status={product.status}')
        
        try:
            serializer = SalesProductSerializer(product)
            data = serializer.data
            print('✅ Serializer success!')
            print(f'Serialized data keys: {list(data.keys())}')
            print(f'Sample data: code={data.get("code")}, name={data.get("name")}, is_active={data.get("is_active")}')
        except Exception as e:
            print(f'❌ Serializer error: {e}')
            import traceback
            traceback.print_exc()
    else:
        print('❌ No products found')

if __name__ == '__main__':
    test_serializer()
