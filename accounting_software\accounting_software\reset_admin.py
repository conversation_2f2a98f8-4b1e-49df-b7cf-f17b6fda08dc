#!/usr/bin/env python3
"""
Reset admin password
"""

import os
import sys
import django

# Add the Django project to the path
sys.path.append('erp_backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')

try:
    django.setup()
    from django.contrib.auth.models import User
    
    print("🔧 Resetting Admin Password...")
    
    # Get admin user
    admin = User.objects.get(username='admin')
    admin.set_password('admin123')
    admin.save()
    
    print("✅ Admin password reset to: admin123")
    print("Username: admin")
    print("Password: admin123")
    
except User.DoesNotExist:
    print("❌ Admin user not found!")
except Exception as e:
    print(f"❌ Error: {e}")
