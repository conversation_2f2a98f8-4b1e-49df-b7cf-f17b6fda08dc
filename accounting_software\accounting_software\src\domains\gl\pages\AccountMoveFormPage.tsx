import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import dayjs from 'dayjs';
import {
  Box,
  Alert,
  Snackbar,
} from '@mui/material';


import {
  Typography,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
} from '@mui/material';

import { AccountMove, AccountMoveLine, AccountJournal } from '../../../shared/types/gl.types';
import { accountMoveService } from '../../../services/account-move.service';

interface AccountMoveFormData {
  name: string;
  move_type: 'entry' | 'out_invoice' | 'out_refund' | 'in_invoice' | 'in_refund' | 'out_receipt' | 'in_receipt';
  state: string;
  ref: string;
  date: string;
  partner_id: number | null;
  journal_id: number | null;
  line_ids: Partial<AccountMoveLine>[];
}

const AccountMoveFormPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  
  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [journals, setJournals] = useState<AccountJournal[]>([]);

  const [accounts, setAccounts] = useState<any[]>([]);

  const [formData, setFormData] = useState<AccountMoveFormData>({
    name: '/',
    move_type: (searchParams.get('type') as AccountMoveFormData['move_type']) || 'entry',
    state: 'draft',
    ref: '',
    date: dayjs().format('YYYY-MM-DD'),
    partner_id: null,
    journal_id: null,
    line_ids: [
      {
        account_id: undefined,
        name: '',
        debit: '0.00',
        credit: '0.00',
      },
      {
        account_id: undefined,
        name: '',
        debit: '0.00',
        credit: '0.00',
      }
    ],
  });

  // Load data
  useEffect(() => {
    loadInitialData();
    if (id) {
      loadAccountMove();
    }
  }, [id]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [journalsData, accountsData] = await Promise.all([
        accountMoveService.getJournals(),
        accountMoveService.getAccounts(),
      ]);

      setJournals(journalsData);
      setAccounts(accountsData);
    } catch (err) {
      console.error('Error loading initial data:', err);
      setError('Failed to load form data');
    } finally {
      setLoading(false);
    }
  };

  const loadAccountMove = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const move = await accountMoveService.getAccountMove(parseInt(id));
      
      setFormData({
        name: move.name,
        move_type: move.move_type,
        state: move.state,
        ref: move.ref || '',
        date: move.date,
        partner_id: move.partner_id || null,
        journal_id: move.journal_id,
        line_ids: move.line_ids || [],
      });
    } catch (err) {
      console.error('Error loading account move:', err);
      setError('Failed to load account move');
    } finally {
      setLoading(false);
    }
  };



  const handleInputChange = (field: keyof AccountMoveFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };



  const handleSave = async (post: boolean = false) => {
    try {
      setLoading(true);
      setError(null);

      // Validate required fields
      if (!formData.journal_id) {
        throw new Error('Journal is required');
      }

      // Validate lines
      if (formData.line_ids.length < 2) {
        throw new Error('At least 2 lines are required');
      }

      for (let i = 0; i < formData.line_ids.length; i++) {
        const line = formData.line_ids[i];

        if (!line.account_id) {
          throw new Error(`Account is required for line ${i + 1}`);
        }

        // Auto-generate description if empty
        if (!line.name || line.name.trim() === '') {
          line.name = `Journal Entry Line ${i + 1}`;
        }

        const debit = parseFloat(line.debit || '0');
        const credit = parseFloat(line.credit || '0');

        if (debit === 0 && credit === 0) {
          throw new Error(`Line ${i + 1} must have either a debit or credit amount`);
        }

        if (debit > 0 && credit > 0) {
          throw new Error(`Line ${i + 1} cannot have both debit and credit amounts`);
        }
      }

      // Check if debits equal credits
      const totalDebit = formData.line_ids.reduce((sum, line) => sum + parseFloat(line.debit || '0'), 0);
      const totalCredit = formData.line_ids.reduce((sum, line) => sum + parseFloat(line.credit || '0'), 0);
      const difference = totalDebit - totalCredit;

      if (Math.abs(difference) > 0.01) {
        throw new Error('Debits must equal Credits');
      }

      // Clean the line data - remove any lines with undefined account_id
      const cleanLines = formData.line_ids.filter(line => line.account_id !== undefined);

      const moveData: Partial<AccountMove> = {
        move_type: formData.move_type,
        ref: formData.ref,
        date: formData.date,
        partner_id: formData.partner_id || undefined,
        journal_id: formData.journal_id!,
        line_ids: cleanLines as AccountMoveLine[],
      };

      let result: AccountMove;
      if (id) {
        result = await accountMoveService.updateAccountMove(parseInt(id), moveData);
      } else {
        result = await accountMoveService.createAccountMove(moveData);
      }

      if (post && result.state === 'draft') {
        await accountMoveService.postAccountMove(result.id);
      }

      setSuccess(id ? 'Account move updated successfully' : 'Account move created successfully');
      
      if (!id) {
        navigate(`/dashboard/gl/account-moves/${result.id}`);
      } else {
        // Reload the data to get updated state
        loadAccountMove();
      }
    } catch (err: any) {
      console.error('Error saving account move:', err);
      setError(err.message || 'Failed to save account move');
    } finally {
      setLoading(false);
    }
  };

  const journalOptions = journals.map(journal => ({
    value: journal.id,
    label: `${journal.code} - ${journal.name}`,
  }));



  const moveTypeOptions = accountMoveService.getMoveTypes().map(type => ({
    value: type.value,
    label: type.label,
  }));

  return (
    <Box sx={{ p: 3 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          {id ? 'Edit' : 'Create'} Journal Entry
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Move Type</InputLabel>
              <Select
                value={formData.move_type}
                onChange={(e) => handleInputChange('move_type', e.target.value)}
                disabled={!!id}
              >
                {moveTypeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Date"
              type="date"
              value={formData.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
              disabled={formData.state === 'posted'}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Journal</InputLabel>
              <Select
                value={formData.journal_id || ''}
                onChange={(e) => handleInputChange('journal_id', e.target.value)}
                disabled={formData.state === 'posted'}
              >
                {journalOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Reference (Optional)"
              value={formData.ref}
              onChange={(e) => handleInputChange('ref', e.target.value)}
              disabled={formData.state === 'posted'}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            onClick={() => navigate('/dashboard/gl/account-moves')}
          >
            Back
          </Button>
          <Button
            variant="contained"
            onClick={() => handleSave(false)}
            disabled={loading || formData.state === 'posted'}
          >
            Save
          </Button>
          {formData.state === 'draft' && (
            <Button
              variant="contained"
              color="success"
              onClick={() => handleSave(true)}
              disabled={loading}
            >
              Post
            </Button>
          )}
        </Box>
      </Paper>

      {/* Snackbars */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={4000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AccountMoveFormPage;
