/**
 * Odoo-style Account Moves Page
 * Universal interface for all accounting documents (invoices, bills, journal entries)
 * Following <PERSON><PERSON><PERSON>'s account.move list view patterns
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Avatar,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PostAdd as PostIcon,
  Cancel as CancelIcon,
  FileCopy as DuplicateIcon,
  Receipt as ReceiptIcon,
  Description as JournalIcon,
  Assignment as BillIcon,
  AccountBalance as AccountBalanceIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { AccountMove, AccountMoveFilters } from '../../../shared/types/gl.types';
import { accountMoveService } from '../../../services/account-move.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import StatCard from '../../../shared/components/StatCard';

const AccountMovesPage: React.FC = () => {
  const navigate = useNavigate();
  
  // State
  const [moves, setMoves] = useState<AccountMove[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<AccountMoveFilters>({});
  const [stats, setStats] = useState<any>(null);

  // Load account moves
  useEffect(() => {
    loadAccountMoves();
  }, [filters]);

  const loadAccountMoves = async () => {
    try {
      setLoading(true);
      setError(null);

      const [movesResponse, statsResponse] = await Promise.all([
        accountMoveService.getAccountMoves(filters),
        accountMoveService.getAccountMoveStats().catch(() => null)
      ]);

      setMoves(movesResponse.results || []);
      setStats(statsResponse);

    } catch (err) {
      console.error('Error loading account moves:', err);
      setError('Failed to load account moves');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: keyof AccountMoveFilters, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const getStateColor = (state: string) => {
    switch (state) {
      case 'draft': return 'warning';
      case 'posted': return 'success';
      case 'cancel': return 'error';
      default: return 'default';
    }
  };

  const getMoveTypeIcon = (moveType: string) => {
    switch (moveType) {
      case 'entry': return <JournalIcon />;
      case 'out_invoice': case 'out_refund': return <ReceiptIcon />;
      case 'in_invoice': case 'in_refund': return <BillIcon />;
      default: return <AccountBalanceIcon />;
    }
  };

  const getMoveTypeLabel = (moveType: string) => {
    const types = accountMoveService.getMoveTypes();
    return types.find(t => t.value === moveType)?.label || moveType;
  };

  const handleAction = async (action: string, moveId: number) => {
    try {
      switch (action) {
        case 'post':
          await accountMoveService.postAccountMove(moveId);
          break;
        case 'cancel':
          await accountMoveService.cancelAccountMove(moveId);
          break;
        case 'reset':
          await accountMoveService.resetToDraft(moveId);
          break;
        case 'duplicate':
          await accountMoveService.duplicateAccountMove(moveId);
          break;
      }
      loadAccountMoves(); // Refresh list
    } catch (err) {
      console.error(`Error ${action}ing move:`, err);
      setError(`Failed to ${action} move`);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
            Account Moves
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            Universal accounting documents (Odoo-style)
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<JournalIcon />}
            onClick={() => navigate('/dashboard/gl/account-moves/create?type=entry')}
          >
            Journal Entry
          </Button>
          <Button
            variant="outlined"
            startIcon={<ReceiptIcon />}
            onClick={() => navigate('/dashboard/gl/account-moves/create?type=out_invoice')}
          >
            Customer Invoice
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/dashboard/gl/account-moves/create')}
          >
            Create Move
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Moves"
              value={stats.total_moves?.toString() || '0'}
              icon={<AccountBalanceIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Draft"
              value={stats.draft_moves?.toString() || '0'}
              icon={<EditIcon />}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Posted"
              value={stats.posted_moves?.toString() || '0'}
              icon={<PostIcon />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Amount"
              value={formatCurrency(stats.total_amount || 0)}
              icon={<ReceiptIcon />}
              color="info"
            />
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Search moves..."
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Move Type</InputLabel>
                <Select
                  value={filters.move_type || ''}
                  onChange={(e) => handleFilterChange('move_type', e.target.value)}
                  label="Move Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  {accountMoveService.getMoveTypes().map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>State</InputLabel>
                <Select
                  value={filters.state || ''}
                  onChange={(e) => handleFilterChange('state', e.target.value)}
                  label="State"
                >
                  <MenuItem value="">All States</MenuItem>
                  {accountMoveService.getStateOptions().map(state => (
                    <MenuItem key={state.value} value={state.value}>
                      {state.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Account Moves Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Number</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Partner</TableCell>
                <TableCell>Journal</TableCell>
                <TableCell>Reference</TableCell>
                <TableCell align="right">Amount</TableCell>
                <TableCell>State</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {moves.map((move) => (
                <TableRow key={move.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                        {getMoveTypeIcon(move.move_type)}
                      </Avatar>
                      {move.name}
                    </Box>
                  </TableCell>
                  <TableCell>{getMoveTypeLabel(move.move_type)}</TableCell>
                  <TableCell>{dayjs(move.date).format('MMM DD, YYYY')}</TableCell>
                  <TableCell>{move.partner_name || '-'}</TableCell>
                  <TableCell>{move.journal_name}</TableCell>
                  <TableCell>{move.ref || '-'}</TableCell>
                  <TableCell align="right">
                    {formatCurrency(parseFloat(move.amount_total))}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={move.state.toUpperCase()}
                      color={getStateColor(move.state) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <Tooltip title="View">
                        <IconButton
                          size="small"
                          onClick={() => navigate(`/dashboard/gl/account-moves/${move.id}`)}
                        >
                          <ViewIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      {move.state === 'draft' && (
                        <>
                          <Tooltip title="Edit">
                            <IconButton
                              size="small"
                              onClick={() => navigate(`/dashboard/gl/account-moves/${move.id}/edit`)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Post">
                            <IconButton
                              size="small"
                              onClick={() => handleAction('post', move.id)}
                            >
                              <PostIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </>
                      )}
                      <Tooltip title="Duplicate">
                        <IconButton
                          size="small"
                          onClick={() => handleAction('duplicate', move.id)}
                        >
                          <DuplicateIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>
    </Box>
  );
};

export default AccountMovesPage;
