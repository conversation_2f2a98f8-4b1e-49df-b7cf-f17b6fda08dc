// src/components/pricing/MarginAnalysis.tsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField
} from '@mui/material';
import { useSnackbar } from 'notistack';
import { DataTable } from '../../../shared/components';
import { pricingService, ProductCost, PriceListItem } from '../../../services/pricingService';

interface MarginAnalysisData {
  product_id: number;
  product_code: string;
  product_name: string;
  cost_price: number;
  sale_price: number;
  margin_amount: number;
  margin_percent: number;
  price_list_name: string;
}

const MarginAnalysis: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [analysisData, setAnalysisData] = useState<MarginAnalysisData[]>([]);
  const [products, setProducts] = useState<ProductCost[]>([]);
  const [priceListItems, setPriceListItems] = useState<PriceListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    minMargin: '',
    maxMargin: '',
    productType: 'all'
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [productsRes, priceListItemsRes] = await Promise.all([
        pricingService.getProductCosts(),
        pricingService.getPriceListItems(),
      ]);
      
      // Handle response format - /all/ endpoint returns direct array
      const productsData = Array.isArray(productsRes.data) ? productsRes.data : productsRes.data.results || productsRes.data;
      const priceListItemsData = priceListItemsRes.data.results || priceListItemsRes.data;

      setProducts(productsData);
      setPriceListItems(priceListItemsData);
      calculateMarginAnalysis(productsData, priceListItemsData);
    } catch (error) {
      enqueueSnackbar('Failed to fetch data', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const getCostByMethod = (product: ProductCost) => {
    switch (product.cost_method) {
      case 'standard':
        return product.standard_cost;
      case 'average':
        return product.average_cost;
      case 'fifo':
        return product.last_cost;
      default:
        return product.standard_cost;
    }
  };

  const calculateMarginAnalysis = (products: ProductCost[], priceListItems: PriceListItem[]) => {
    const analysis: MarginAnalysisData[] = [];

    priceListItems.forEach(item => {
      const product = products.find(p => p.id === item.product);
      if (product) {
        const costPrice = getCostByMethod(product);
        const salePrice = item.unit_price;
        const marginAmount = salePrice - costPrice;
        const marginPercent = salePrice > 0 ? (marginAmount / salePrice) * 100 : 0;

        analysis.push({
          product_id: product.id,
          product_code: product.code,
          product_name: product.name,
          cost_price: costPrice,
          sale_price: salePrice,
          margin_amount: marginAmount,
          margin_percent: marginPercent,
          price_list_name: `Price List ${item.price_list}` // You might want to fetch actual price list names
        });
      }
    });

    setAnalysisData(analysis);
  };

  const getMarginColor = (marginPercent: number) => {
    if (marginPercent < 10) return 'error';
    if (marginPercent < 25) return 'warning';
    return 'success';
  };

  const getMarginLabel = (marginPercent: number) => {
    if (marginPercent < 0) return 'Loss';
    if (marginPercent < 10) return 'Low';
    if (marginPercent < 25) return 'Medium';
    return 'High';
  };

  const filteredData = analysisData.filter(item => {
    const minMargin = filters.minMargin ? parseFloat(filters.minMargin) : -Infinity;
    const maxMargin = filters.maxMargin ? parseFloat(filters.maxMargin) : Infinity;
    const product = products.find(p => p.id === item.product_id);
    
    return (
      item.margin_percent >= minMargin &&
      item.margin_percent <= maxMargin &&
      (filters.productType === 'all' || product?.type === filters.productType)
    );
  });

  const columns = [
    {
      field: 'product_code',
      headerName: 'Product Code',
      width: 120,
    },
    {
      field: 'product_name',
      headerName: 'Product Name',
      flex: 1,
    },
    {
      field: 'cost_price',
      headerName: 'Cost Price',
      width: 120,
      renderCell: (params: any) => `$${params.value.toFixed(2)}`,
    },
    {
      field: 'sale_price',
      headerName: 'Sale Price',
      width: 120,
      renderCell: (params: any) => `$${params.value.toFixed(2)}`,
    },
    {
      field: 'margin_amount',
      headerName: 'Margin ($)',
      width: 120,
      renderCell: (params: any) => (
        <Typography 
          variant="body2" 
          color={params.value >= 0 ? 'success.main' : 'error.main'}
          fontWeight="bold"
        >
          ${params.value.toFixed(2)}
        </Typography>
      ),
    },
    {
      field: 'margin_percent',
      headerName: 'Margin %',
      width: 120,
      renderCell: (params: any) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" fontWeight="bold">
            {params.value.toFixed(1)}%
          </Typography>
          <Chip 
            label={getMarginLabel(params.value)} 
            color={getMarginColor(params.value)} 
            size="small" 
          />
        </Box>
      ),
    },
    {
      field: 'margin_visual',
      headerName: 'Margin Visual',
      width: 150,
      renderCell: (params: any) => (
        <Box sx={{ width: '100%' }}>
          <LinearProgress
            variant="determinate"
            value={Math.min(Math.max(params.row.margin_percent, 0), 100)}
            color={getMarginColor(params.row.margin_percent)}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>
      ),
    },
    {
      field: 'price_list_name',
      headerName: 'Price List',
      width: 150,
    },
  ];

  const summaryStats = {
    totalProducts: filteredData.length,
    avgMargin: filteredData.length > 0 
      ? filteredData.reduce((sum, item) => sum + item.margin_percent, 0) / filteredData.length 
      : 0,
    lowMarginCount: filteredData.filter(item => item.margin_percent < 10).length,
    negativeMarginCount: filteredData.filter(item => item.margin_percent < 0).length,
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          Cost vs Sale Price Analysis
        </Typography>

        {/* Summary Cards */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  {summaryStats.totalProducts}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Products
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main">
                  {summaryStats.avgMargin.toFixed(1)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Average Margin
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">
                  {summaryStats.lowMarginCount}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Low Margin (&lt;10%)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card variant="outlined">
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="error.main">
                  {summaryStats.negativeMarginCount}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Negative Margin
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Min Margin %"
              type="number"
              value={filters.minMargin}
              onChange={(e) => setFilters(prev => ({ ...prev, minMargin: e.target.value }))}
              size="small"
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              label="Max Margin %"
              type="number"
              value={filters.maxMargin}
              onChange={(e) => setFilters(prev => ({ ...prev, maxMargin: e.target.value }))}
              size="small"
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Product Type</InputLabel>
              <Select
                value={filters.productType}
                label="Product Type"
                onChange={(e) => setFilters(prev => ({ ...prev, productType: e.target.value }))}
              >
                <MenuItem value="all">All Types</MenuItem>
                <MenuItem value="product">Product</MenuItem>
                <MenuItem value="service">Service</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Data Table */}
        <DataTable
          columns={columns}
          rows={filteredData}
          loading={loading}
          pageSize={10}
        />
      </CardContent>
    </Card>
  );
};

export default MarginAnalysis;
