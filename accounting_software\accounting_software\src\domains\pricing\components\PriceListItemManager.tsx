// src/components/pricing/PriceListItemManager.tsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { DataTable } from '../../../shared/components';
import { pricingService, PriceListItem, PriceList, ProductCost } from '../../../services/pricingService';
import dayjs from 'dayjs';

// Use ProductCost from pricingService for products

const PriceListItemManager: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [priceListItems, setPriceListItems] = useState<PriceListItem[]>([]);
  const [priceLists, setPriceLists] = useState<PriceList[]>([]);
  const [products, setProducts] = useState<ProductCost[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentId, setCurrentId] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    price_list: '',
    product: '',
    unit_price: 0,
    min_quantity: 1,
    discount_percent: 0,
    effective_date: dayjs(),
    expiry_date: null as dayjs.Dayjs | null
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [priceListsRes, productsRes, itemsRes] = await Promise.all([
        // Fetch all price lists with large page size
        pricingService.getPriceLists({ page_size: 1000 }),
        // Fetch products directly from pricing module
        pricingService.getProductCosts(),
        // Fetch all price list items with large page size
        pricingService.getPriceListItems({ page_size: 1000 })
      ]);

      // Handle Django REST framework pagination for price lists and items
      const priceListsData = (priceListsRes.data as any).results || priceListsRes.data;
      const productsData = (productsRes.data as any).results || productsRes.data;
      const itemsData = (itemsRes.data as any).results || itemsRes.data;

      console.log('PriceListItemManager - Products from pricing service:', productsData);
      console.log('PriceListItemManager - Products Count:', productsData.length);

      // Ensure we have arrays
      const finalPriceLists = Array.isArray(priceListsData) ? priceListsData : [];
      const finalProducts = Array.isArray(productsData) ? productsData : [];
      const finalItems = Array.isArray(itemsData) ? itemsData : [];

      console.log('Final products count:', finalProducts.length);
      console.log('Sample products:', finalProducts.slice(0, 3));

      setPriceLists(finalPriceLists);
      setProducts(finalProducts);
      setPriceListItems(finalItems);
    } catch (error) {
      console.error('Failed to fetch data:', error);
      enqueueSnackbar('Failed to fetch data', { variant: 'error' });
      setPriceLists([]);
      setProducts([]);
      setPriceListItems([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setFormData({
      price_list: '',
      product: '',
      unit_price: 0,
      min_quantity: 1,
      discount_percent: 0,
      effective_date: dayjs(),
      expiry_date: null
    });
    setEditMode(false);
    setCurrentId(null);
    setDialogOpen(true);
  };

  const handleEdit = (record: PriceListItem) => {
    setFormData({
      price_list: record.price_list.toString(),
      product: record.product.toString(),
      unit_price: record.unit_price,
      min_quantity: record.min_quantity,
      discount_percent: record.discount_percent,
      effective_date: record.effective_date ? dayjs(record.effective_date) : dayjs(),
      expiry_date: record.expiry_date ? dayjs(record.expiry_date) : null
    });
    setEditMode(true);
    setCurrentId(record.id);
    setDialogOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this price list item?')) {
      try {
        await pricingService.deletePriceListItem(id);
        enqueueSnackbar('Price list item deleted successfully', { variant: 'success' });
        fetchData();
      } catch (error) {
        enqueueSnackbar('Failed to delete price list item', { variant: 'error' });
      }
    }
  };

  const handleSubmit = async () => {
    if (!formData.price_list || !formData.product) {
      enqueueSnackbar('Please select price list and product', { variant: 'warning' });
      return;
    }

    try {
      const data = {
        price_list: parseInt(formData.price_list),
        product: parseInt(formData.product),
        unit_price: formData.unit_price,
        min_quantity: formData.min_quantity,
        discount_percent: formData.discount_percent,
        effective_date: formData.effective_date.format('YYYY-MM-DD'),
        expiry_date: formData.expiry_date ? formData.expiry_date.format('YYYY-MM-DD') : undefined
      };

      if (editMode && currentId) {
        await pricingService.updatePriceListItem(currentId, data);
        enqueueSnackbar('Price list item updated successfully', { variant: 'success' });
      } else {
        await pricingService.createPriceListItem(data);
        enqueueSnackbar('Price list item created successfully', { variant: 'success' });
      }

      setDialogOpen(false);
      fetchData();
    } catch (error) {
      enqueueSnackbar('Failed to save price list item', { variant: 'error' });
    }
  };

  const columns = [
    {
      field: 'price_list',
      headerName: 'Price List',
      width: 150,
      renderCell: (params: any) => {
        const priceList = (priceLists || []).find(pl => pl.id === params.value);
        return priceList ? priceList.name : 'Unknown';
      },
    },
    {
      field: 'product',
      headerName: 'Product',
      flex: 1,
      renderCell: (params: any) => {
        const product = (products || []).find(p => p.id === params.value);
        return product ? (product.code ? `${product.code} - ${product.name}` : product.name) : 'Unknown';
      },
    },
    {
      field: 'unit_price',
      headerName: 'Unit Price',
      width: 120,
      renderCell: (params: any) => `$${Number(params.value).toFixed(2)}`,
    },
    {
      field: 'min_quantity',
      headerName: 'Min Quantity',
      width: 120,
      renderCell: (params: any) => Number(params.value).toFixed(2),
    },
    {
      field: 'discount_percent',
      headerName: 'Discount %',
      width: 120,
      renderCell: (params: any) => `${Number(params.value).toFixed(2)}%`,
    },
    {
      field: 'effective_date',
      headerName: 'Effective Date',
      width: 130,
      renderCell: (params: any) => dayjs(params.value).format('YYYY-MM-DD'),
    },
    {
      field: 'expiry_date',
      headerName: 'Expiry Date',
      width: 130,
      renderCell: (params: any) => 
        params.value ? dayjs(params.value).format('YYYY-MM-DD') : 'No Expiry',
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params: any) => (
        <Box>
          <IconButton 
            size="small" 
            onClick={() => handleEdit(params.row)}
            color="primary"
          >
            <EditIcon />
          </IconButton>
          <IconButton 
            size="small" 
            onClick={() => handleDelete(params.row.id)}
            color="error"
          >
            <DeleteIcon />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5">Price List Items Management</Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              onClick={() => {
                console.log('Debug - Current products:', products);
                console.log('Debug - Products count:', products.length);
                fetchData();
              }}
            >
              Debug Products ({products.length})
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreate}
            >
              Add Price List Item
            </Button>
          </Box>
        </Box>

        <DataTable
          columns={columns}
          rows={priceListItems}
          loading={loading}
          pageSize={10}
        />

        <Dialog 
          open={dialogOpen} 
          onClose={() => setDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            {editMode ? 'Edit Price List Item' : 'Add Price List Item'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Price List</InputLabel>
                  <Select
                    value={formData.price_list}
                    label="Price List"
                    onChange={(e) => setFormData(prev => ({ ...prev, price_list: e.target.value }))}
                  >
                    {(priceLists || []).map(priceList => (
                      <MenuItem key={priceList.id} value={priceList.id.toString()}>
                        {priceList.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Product</InputLabel>
                  <Select
                    value={formData.product}
                    label="Product"
                    onChange={(e) => setFormData(prev => ({ ...prev, product: e.target.value }))}
                  >
                    {products.length === 0 ? (
                      <MenuItem disabled>
                        {loading ? 'Loading products...' : 'No products available'}
                      </MenuItem>
                    ) : (
                      products.map(product => (
                        <MenuItem key={product.id} value={product.id.toString()}>
                          {product.code ? `${product.code} - ${product.name}` : product.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Unit Price"
                  type="number"
                  value={formData.unit_price}
                  onChange={(e) => setFormData(prev => ({ ...prev, unit_price: parseFloat(e.target.value) || 0 }))}
                  inputProps={{ min: 0, step: 0.01 }}
                  required
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Minimum Quantity"
                  type="number"
                  value={formData.min_quantity}
                  onChange={(e) => setFormData(prev => ({ ...prev, min_quantity: parseFloat(e.target.value) || 1 }))}
                  inputProps={{ min: 0.01, step: 0.01 }}
                  required
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Discount Percentage"
                  type="number"
                  value={formData.discount_percent}
                  onChange={(e) => setFormData(prev => ({ ...prev, discount_percent: parseFloat(e.target.value) || 0 }))}
                  inputProps={{ min: 0, max: 100, step: 0.01 }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Effective Date"
                  value={formData.effective_date}
                  onChange={(date) => setFormData(prev => ({ ...prev, effective_date: date || dayjs() }))}
                  slotProps={{ textField: { fullWidth: true, required: true } }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Expiry Date"
                  value={formData.expiry_date}
                  onChange={(date) => setFormData(prev => ({ ...prev, expiry_date: date }))}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSubmit} variant="contained">
              {editMode ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default PriceListItemManager;
