// src/components/pricing/PricingCalculator.tsx
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Grid,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import { useSnackbar } from 'notistack';
import api from '../../../services/api';
import { pricingService, PriceCalculationResult } from '../../../services/pricingService';
import productService, { UnifiedProduct } from '../../../services/productService';

// Use UnifiedProduct from productService instead of local interface

interface Customer {
  contact: number; // This is the primary key
  contact_name?: string; // This might be included in serializer
  customer_code?: string;
  first_name?: string;
  last_name?: string;
  company_name?: string;
}

const PricingCalculator: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [products, setProducts] = useState<UnifiedProduct[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [priceResult, setPriceResult] = useState<PriceCalculationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [formData, setFormData] = useState({
    product: '',
    customer: '',
    quantity: 1
  });

  // Fetch products and customers on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setDataLoading(true);
        const [productsData, customersRes] = await Promise.all([
          // Use the unified product service to get all available products
          productService.getActiveProducts(),
          // Fetch all customers with large page size
          api.get('/contacts/customers/', { params: { page_size: 1000 } }),
        ]);

        // Handle Django REST framework pagination for customers
        const customersData = (customersRes.data as any).results || customersRes.data;

        console.log('PricingCalculator - Products from unified service:', productsData);
        console.log('PricingCalculator - Products Count:', productsData.length);

        setProducts(Array.isArray(productsData) ? productsData : []); // Handle null data
        setCustomers(Array.isArray(customersData) ? customersData : []); // Handle null data
      } catch (error) {
        console.error('Failed to fetch data:', error);
        enqueueSnackbar('Failed to fetch data', { variant: 'error' });
        setProducts([]); // Set empty array on error
        setCustomers([]); // Set empty array on error
      } finally {
        setDataLoading(false);
      }
    };
    fetchData();
  }, [enqueueSnackbar]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const calculatePrice = async () => {
    if (!formData.product) {
      enqueueSnackbar('Please select a product', { variant: 'warning' });
      return;
    }

    try {
      setLoading(true);
      
      const params = {
        product: parseInt(formData.product),
        quantity: formData.quantity,
        customer: formData.customer ? parseInt(formData.customer) : undefined
      };
      
      const response = await pricingService.calculatePrice(params);
      setPriceResult(response.data);
      enqueueSnackbar('Price calculated successfully', { variant: 'success' });
    } catch (error) {
      console.error('Failed to calculate price:', error);
      enqueueSnackbar('Failed to calculate price', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const resultData = priceResult ? [
    { field: 'Product Code', value: priceResult.product_code },
    { field: 'Product Name', value: priceResult.product_name },
    { field: 'Customer', value: priceResult.customer_id ?
        customers.find(c => c.contact === priceResult.customer_id)?.contact_name : 'None' },
    { field: 'Quantity', value: priceResult.quantity },
    { field: 'Sale Price', value: `$${priceResult.unit_price.toFixed(2)}` },
    { field: 'Cost Price', value: priceResult.cost_price ? `$${priceResult.cost_price.toFixed(2)}` : 'N/A' },
    { field: 'Margin ($)', value: priceResult.margin ? `$${priceResult.margin.toFixed(2)}` : 'N/A' },
    { field: 'Margin (%)', value: priceResult.margin_percent ? `${priceResult.margin_percent.toFixed(1)}%` : 'N/A' },
    { field: 'Currency', value: priceResult.currency },
  ] : [];

  if (dataLoading) {
    return (
      <Card sx={{ margin: 2 }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
            <CircularProgress />
            <Typography sx={{ ml: 2 }}>Loading data...</Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ margin: 2 }}>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          Pricing Calculator
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth required>
              <InputLabel>Product</InputLabel>
              <Select
                value={formData.product}
                label="Product"
                onChange={(e) => handleInputChange('product', e.target.value)}
              >
                {products && products.length > 0 ? products.map(product => (
                  <MenuItem key={product.id} value={product.id?.toString() || ''}>
                    {product.sku ? `${product.sku} - ${product.name}` : product.name}
                  </MenuItem>
                )) : (
                  <MenuItem disabled value="">
                    No products available
                  </MenuItem>
                )}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Customer (optional)</InputLabel>
              <Select
                value={formData.customer}
                label="Customer (optional)"
                onChange={(e) => handleInputChange('customer', e.target.value)}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {customers && customers.length > 0 ? customers.map(customer => (
                  <MenuItem key={customer.contact} value={customer.contact?.toString() || ''}>
                    {customer.contact_name || 'Unknown Customer'}
                  </MenuItem>
                )) : (
                  <MenuItem disabled value="">
                    No customers available
                  </MenuItem>
                )}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Quantity"
              type="number"
              value={formData.quantity}
              onChange={(e) => handleInputChange('quantity', parseFloat(e.target.value) || 1)}
              inputProps={{ min: 0.01, step: 0.01 }}
              required
            />
          </Grid>
        </Grid>
        
        <Box sx={{ mt: 3, mb: 2 }}>
          <Button 
            variant="contained" 
            onClick={calculatePrice}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Calculating...' : 'Calculate Price'}
          </Button>
        </Box>
        
        {priceResult && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Pricing Result
            </Typography>
            <TableContainer component={Paper}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell><strong>Field</strong></TableCell>
                    <TableCell><strong>Value</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {resultData.map((row, index) => (
                    <TableRow key={index}>
                      <TableCell>{row.field}</TableCell>
                      <TableCell>{row.value}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default PricingCalculator;
