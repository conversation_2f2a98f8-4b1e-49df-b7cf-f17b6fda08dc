import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import dayjs from 'dayjs';
import {
  Box,
  Grid,
  Chip,
  Button,
  Divider,
  Alert,
  Snackbar,
  Card,
  CardContent,
  Typography,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Receipt as ReceiptIcon,
  LocalShipping as DeliveryIcon,
  Email as EmailIcon,
  Print as PrintIcon,
  FileCopy as DuplicateIcon,
  Cancel as CancelIcon,
  CheckCircle as ConfirmIcon,
  Add as AddIcon,
} from '@mui/icons-material';

import {
  OdooFormLayout,
  OdooFormSection,
  OdooFormField,
  OdooFormRow,
  OdooTextField,
  OdooSelectField,
  OdooDateField,
  OdooAutocompleteField,
} from '../../../shared/components';
import OdooSalesOrderLinesTable from '../../../shared/components/OdooSalesOrderLinesTable';

import { 
  OdooSalesOrder, 
  SalesOrderLine, 
  SalesOrderState,
  InvoicePolicy,
  DeliveryPolicy,
  odooSalesOrderService 
} from '../../../services/odoo-sales-order.service';

interface OdooSalesOrderFormProps {
  mode?: 'create' | 'edit' | 'view';
}

const OdooSalesOrderForm: React.FC<OdooSalesOrderFormProps> = ({ mode = 'create' }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  
  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [customers, setCustomers] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [paymentTerms, setPaymentTerms] = useState<any[]>([]);

  const [formData, setFormData] = useState<Partial<OdooSalesOrder>>({
    name: 'New',
    partner_id: 0,
    date_order: dayjs().format('YYYY-MM-DD'),
    validity_date: dayjs().add(30, 'days').format('YYYY-MM-DD'),
    state: 'draft',
    invoice_policy: 'order',
    delivery_policy: 'direct',
    amount_untaxed: 0,
    amount_tax: 0,
    amount_total: 0,
    amount_invoiced: 0,
    amount_to_invoice: 0,
    delivery_count: 0,
    invoice_count: 0,
    order_line: [],
  });

  // Load initial data
  useEffect(() => {
    loadInitialData();
    if (id && mode !== 'create') {
      loadSalesOrder();
    }
  }, [id, mode]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [customersData, productsData, paymentTermsData] = await Promise.all([
        odooSalesOrderService.getCustomers(),
        odooSalesOrderService.getAvailableProducts(),
        odooSalesOrderService.getPaymentTerms(),
      ]);
      
      setCustomers(customersData);
      setProducts(productsData);
      setPaymentTerms(paymentTermsData);
    } catch (err) {
      console.error('Error loading initial data:', err);
      setError('Failed to load form data');
    } finally {
      setLoading(false);
    }
  };

  const loadSalesOrder = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const salesOrder = await odooSalesOrderService.getSalesOrder(parseInt(id));
      setFormData(salesOrder);
    } catch (err) {
      console.error('Error loading sales order:', err);
      setError('Failed to load sales order');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof OdooSalesOrder, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLineChange = (index: number, field: keyof SalesOrderLine, value: any) => {
    const newLines = [...(formData.order_line || [])];
    newLines[index] = { ...newLines[index], [field]: value };
    
    // Recalculate line totals
    if (field === 'product_uom_qty' || field === 'price_unit' || field === 'discount') {
      const line = newLines[index];
      const subtotal = line.product_uom_qty * line.price_unit;
      const discountAmount = subtotal * (line.discount / 100);
      line.price_subtotal = subtotal - discountAmount;
      line.price_total = line.price_subtotal; // Add tax calculation here
    }
    
    setFormData(prev => ({ ...prev, order_line: newLines }));
    calculateTotals(newLines);
  };

  const addLine = () => {
    const newLine: Partial<SalesOrderLine> = {
      product_id: 0,
      name: '',
      product_uom_qty: 1,
      qty_delivered: 0,
      qty_invoiced: 0,
      qty_to_invoice: 0,
      price_unit: 0,
      discount: 0,
      price_subtotal: 0,
      price_total: 0,
      sequence: (formData.order_line?.length || 0) + 1,
    };
    
    setFormData(prev => ({
      ...prev,
      order_line: [...(prev.order_line || []), newLine as SalesOrderLine]
    }));
  };

  const removeLine = (index: number) => {
    const newLines = formData.order_line?.filter((_, i) => i !== index) || [];
    setFormData(prev => ({ ...prev, order_line: newLines }));
    calculateTotals(newLines);
  };

  const calculateTotals = (lines: SalesOrderLine[]) => {
    const amount_untaxed = lines.reduce((sum, line) => sum + line.price_subtotal, 0);
    const amount_tax = lines.reduce((sum, line) => sum + (line.price_total - line.price_subtotal), 0);
    const amount_total = amount_untaxed + amount_tax;
    
    setFormData(prev => ({
      ...prev,
      amount_untaxed,
      amount_tax,
      amount_total,
      amount_to_invoice: amount_total - (prev.amount_invoiced || 0),
    }));
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!formData.partner_id) {
        throw new Error('Customer is required');
      }

      let result: OdooSalesOrder;
      if (id && mode === 'edit') {
        result = await odooSalesOrderService.updateSalesOrder(parseInt(id), formData);
      } else {
        result = await odooSalesOrderService.createSalesOrder(formData);
      }

      setSuccess(id ? 'Sales order updated successfully' : 'Sales order created successfully');
      
      if (!id) {
        navigate(`/dashboard/sales/orders/${result.id}`);
      } else {
        loadSalesOrder();
      }
    } catch (err: any) {
      console.error('Error saving sales order:', err);
      setError(err.message || 'Failed to save sales order');
    } finally {
      setLoading(false);
    }
  };

  // State transition actions
  const handleConfirm = async () => {
    if (!id) return;
    try {
      setLoading(true);
      await odooSalesOrderService.actionConfirm(parseInt(id));
      setSuccess('Sales order confirmed successfully');
      loadSalesOrder();
    } catch (err: any) {
      setError(err.message || 'Failed to confirm sales order');
    } finally {
      setLoading(false);
    }
  };

  const handleSendByEmail = async () => {
    if (!id) return;
    try {
      setLoading(true);
      await odooSalesOrderService.actionQuotationSent(parseInt(id));
      setSuccess('Quotation sent successfully');
      loadSalesOrder();
    } catch (err: any) {
      setError(err.message || 'Failed to send quotation');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = async () => {
    if (!id) return;
    try {
      setLoading(true);
      await odooSalesOrderService.actionCancel(parseInt(id));
      setSuccess('Sales order cancelled');
      loadSalesOrder();
    } catch (err: any) {
      setError(err.message || 'Failed to cancel sales order');
    } finally {
      setLoading(false);
    }
  };

  // Smart buttons data
  const getSmartButtons = () => {
    const buttons = [];
    
    if (formData.delivery_count && formData.delivery_count > 0) {
      buttons.push({
        label: `${formData.delivery_count} Delivery`,
        icon: <DeliveryIcon />,
        onClick: () => navigate(`/dashboard/sales/orders/${id}/deliveries`),
      });
    }
    
    if (formData.invoice_count && formData.invoice_count > 0) {
      buttons.push({
        label: `${formData.invoice_count} Invoice`,
        icon: <ReceiptIcon />,
        onClick: () => navigate(`/dashboard/sales/orders/${id}/invoices`),
      });
    }
    
    return buttons;
  };

  // Action buttons based on state
  const getActionButtons = () => {
    const buttons = [];
    
    if (formData.state === 'draft') {
      buttons.push(
        <Button
          key="send"
          variant="outlined"
          startIcon={<EmailIcon />}
          onClick={handleSendByEmail}
          disabled={loading}
        >
          Send by Email
        </Button>
      );
      buttons.push(
        <Button
          key="confirm"
          variant="contained"
          startIcon={<ConfirmIcon />}
          onClick={handleConfirm}
          disabled={loading}
          sx={{ bgcolor: '#875a7b', '&:hover': { bgcolor: '#6f4c69' } }}
        >
          Confirm
        </Button>
      );
    }
    
    if (formData.state === 'sent') {
      buttons.push(
        <Button
          key="confirm"
          variant="contained"
          startIcon={<ConfirmIcon />}
          onClick={handleConfirm}
          disabled={loading}
          sx={{ bgcolor: '#875a7b', '&:hover': { bgcolor: '#6f4c69' } }}
        >
          Confirm
        </Button>
      );
    }
    
    if (['draft', 'sent'].includes(formData.state || '')) {
      buttons.push(
        <Button
          key="cancel"
          variant="outlined"
          startIcon={<CancelIcon />}
          onClick={handleCancel}
          disabled={loading}
          color="error"
        >
          Cancel
        </Button>
      );
    }
    
    return buttons;
  };

  const customerOptions = customers.map(customer => ({
    id: customer.id,
    label: customer.name || customer.display_name,
  }));

  const paymentTermOptions = paymentTerms.map(term => ({
    value: term.id,
    label: term.name,
  }));

  const invoicePolicyOptions = [
    { value: 'order', label: 'Ordered quantities' },
    { value: 'delivery', label: 'Delivered quantities' },
  ];

  const deliveryPolicyOptions = [
    { value: 'direct', label: 'As soon as possible' },
    { value: 'one', label: 'When all products are ready' },
  ];

  return (
    <>
      <OdooFormLayout
        title={formData.name || 'New Sales Order'}
        subtitle={odooSalesOrderService.getStateLabel(formData.state as SalesOrderState)}
        icon={<ReceiptIcon />}
        status={{
          label: odooSalesOrderService.getStateLabel(formData.state as SalesOrderState),
          color: odooSalesOrderService.getStateColor(formData.state as SalesOrderState),
        }}
        onBack={() => navigate('/dashboard/sales/orders')}
        onSave={handleSave}
        loading={loading}
        disabled={mode === 'view' || formData.state === 'done'}
      >
        <Box sx={{ maxWidth: '1200px', mx: 'auto' }}>
          {/* Smart Buttons */}
          {getSmartButtons().length > 0 && (
            <Box sx={{ mb: 3, display: 'flex', gap: 2 }}>
              {getSmartButtons().map((button, index) => (
                <Card key={index} sx={{ cursor: 'pointer' }} onClick={button.onClick}>
                  <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 1, py: 1 }}>
                    {button.icon}
                    <Typography variant="body2">{button.label}</Typography>
                  </CardContent>
                </Card>
              ))}
            </Box>
          )}

          {/* Action Buttons */}
          <Box sx={{ mb: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            {getActionButtons()}
          </Box>

          {/* Order Information */}
          <OdooFormSection title="Order Information">
            <OdooFormRow>
              <OdooFormField xs={12} md={6}>
                <OdooAutocompleteField
                  label="Customer"
                  value={customerOptions.find(c => c.id === formData.partner_id) || null}
                  onChange={(value) => handleInputChange('partner_id', value?.id || 0)}
                  options={customerOptions}
                  disabled={mode === 'view' || formData.state !== 'draft'}
                  required
                />
              </OdooFormField>
              
              <OdooFormField xs={12} md={6}>
                <OdooTextField
                  label="Customer Reference"
                  value={formData.client_order_ref || ''}
                  onChange={(value) => handleInputChange('client_order_ref', value)}
                  disabled={mode === 'view'}
                />
              </OdooFormField>
            </OdooFormRow>

            <OdooFormRow>
              <OdooFormField xs={12} md={4}>
                <OdooDateField
                  label="Order Date"
                  value={formData.date_order || ''}
                  onChange={(value) => handleInputChange('date_order', value)}
                  disabled={mode === 'view' || formData.state !== 'draft'}
                  required
                />
              </OdooFormField>
              
              <OdooFormField xs={12} md={4}>
                <OdooDateField
                  label="Expiration"
                  value={formData.validity_date || ''}
                  onChange={(value) => handleInputChange('validity_date', value)}
                  disabled={mode === 'view'}
                />
              </OdooFormField>
              
              <OdooFormField xs={12} md={4}>
                <OdooDateField
                  label="Delivery Date"
                  value={formData.commitment_date || ''}
                  onChange={(value) => handleInputChange('commitment_date', value)}
                  disabled={mode === 'view'}
                />
              </OdooFormField>
            </OdooFormRow>
          </OdooFormSection>

          {/* Order Lines */}
          <OdooFormSection title="Order Lines" noPadding>
            <Box sx={{ p: 3 }}>
              <OdooSalesOrderLinesTable
                lines={formData.order_line || []}
                products={products}
                onLineChange={handleLineChange}
                onAddLine={addLine}
                onRemoveLine={removeLine}
                disabled={mode === 'view' || formData.state === 'done'}
                showDelivered={formData.state === 'sale' || formData.state === 'done'}
                showInvoiced={formData.state === 'sale' || formData.state === 'done'}
                currency="USD"
              />
            </Box>
          </OdooFormSection>

          {/* Other Information */}
          <OdooFormSection title="Other Information">
            <OdooFormRow>
              <OdooFormField xs={12} md={6}>
                <OdooSelectField
                  label="Invoice Policy"
                  value={formData.invoice_policy || 'order'}
                  onChange={(value) => handleInputChange('invoice_policy', value)}
                  options={invoicePolicyOptions}
                  disabled={mode === 'view' || formData.state !== 'draft'}
                />
              </OdooFormField>
              
              <OdooFormField xs={12} md={6}>
                <OdooSelectField
                  label="Delivery Policy"
                  value={formData.delivery_policy || 'direct'}
                  onChange={(value) => handleInputChange('delivery_policy', value)}
                  options={deliveryPolicyOptions}
                  disabled={mode === 'view' || formData.state !== 'draft'}
                />
              </OdooFormField>
            </OdooFormRow>

            <OdooFormRow>
              <OdooFormField xs={12} md={6}>
                <OdooSelectField
                  label="Payment Terms"
                  value={formData.payment_term_id || ''}
                  onChange={(value) => handleInputChange('payment_term_id', value)}
                  options={paymentTermOptions}
                  disabled={mode === 'view'}
                />
              </OdooFormField>
            </OdooFormRow>

            <OdooFormRow>
              <OdooFormField xs={12}>
                <OdooTextField
                  label="Terms and Conditions"
                  value={formData.note || ''}
                  onChange={(value) => handleInputChange('note', value)}
                  disabled={mode === 'view'}
                  multiline
                  rows={4}
                />
              </OdooFormField>
            </OdooFormRow>
          </OdooFormSection>
        </Box>
      </OdooFormLayout>

      {/* Snackbars */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={4000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>
    </>
  );
};

export default OdooSalesOrderForm;
