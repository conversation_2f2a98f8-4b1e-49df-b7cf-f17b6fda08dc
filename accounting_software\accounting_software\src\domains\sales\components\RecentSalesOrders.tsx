import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Visibility, GetApp } from '@mui/icons-material';
import api from '../../../services/api';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

interface SalesOrder {
  id: number;
  so_number: string;
  customer_name: string;
  so_date: string;
  total_amount: number;
  status: string;
}

const RecentSalesOrders: React.FC = () => {
  const [salesOrders, setSalesOrders] = useState<SalesOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    const fetchRecentOrders = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch recent sales orders (last 5)
        const response = await api.get('/sales/sales-orders/', {
          params: {
            page_size: 5,
            ordering: '-so_date,-created_at'
          }
        });

        const orders = response.data.results || response.data || [];
        console.log('Recent sales orders:', orders);
        setSalesOrders(orders);
      } catch (err) {
        console.error('Failed to fetch recent sales orders:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch recent orders');
      } finally {
        setLoading(false);
      }
    };

    fetchRecentOrders();
  }, []);

  const formatCurrency = (amount: number) => {
    const symbol = currencyInfo?.functional_currency_symbol || '$';
    return `${symbol}${amount.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'error';
      case 'acknowledged':
        return 'info';
      case 'sent':
        return 'primary';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>SO Number</TableCell>
            <TableCell>Customer</TableCell>
            <TableCell>Date</TableCell>
            <TableCell align="right">Amount</TableCell>
            <TableCell>Status</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {salesOrders.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                <Typography color="text.secondary">
                  No recent sales orders found
                </Typography>
              </TableCell>
            </TableRow>
          ) : (
            salesOrders.map((order) => (
              <TableRow key={order.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {order.so_number}
                  </Typography>
                </TableCell>
                <TableCell>{order.customer_name}</TableCell>
                <TableCell>{new Date(order.so_date).toLocaleDateString()}</TableCell>
                <TableCell align="right">
                  <Typography variant="body2" fontWeight="medium">
                    {formatCurrency(order.total_amount)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={order.status.toUpperCase()}
                    color={getStatusColor(order.status) as any}
                    size="small"
                  />
                </TableCell>
              <TableCell align="right">
                <Tooltip title="View Order">
                  <IconButton size="small">
                    <Visibility fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Download">
                  <IconButton size="small">
                    <GetApp fontSize="small" />
                  </IconButton>
                </Tooltip>
              </TableCell>
            </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </Box>
  );
};

export default RecentSalesOrders;
