import React, { useState, useEffect } from 'react';
import { Grid, Paper, Typography, Box, CircularProgress } from '@mui/material';
import {
  TrendingUp,
  Receipt,
  LocalShipping,
  AttachMoney,
  Assignment,
  CheckCircle,
  Schedule,
  Cancel,
} from '@mui/icons-material';
import api from '../../../services/api';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

interface SalesOrderStatsData {
  total_orders: number;
  draft_orders: number;
  pending_orders: number;
  sent_orders: number;
  acknowledged_orders: number;
  delivered_orders: number;
  cancelled_orders: number;
  total_value: number;
  pending_value: number;
  delivered_value: number;
  average_order_value: number;
}

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  trend?: string;
  trendValue?: string;
  color: string;
  loading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon, 
  trend, 
  trendValue, 
  color, 
  loading = false 
}) => (
  <Paper sx={{ p: 2, borderRadius: 2, height: '100%' }}>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
      <Box sx={{ flex: 1 }}>
        <Typography variant="subtitle2" color="textSecondary">
          {title}
        </Typography>
        {loading ? (
          <Box sx={{ display: 'flex', alignItems: 'center', my: 1 }}>
            <CircularProgress size={24} />
          </Box>
        ) : (
          <Typography variant="h4" sx={{ my: 1, fontWeight: 'bold' }}>
            {value}
          </Typography>
        )}
        {trend && trendValue && !loading && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="subtitle2"
              color={trend === 'up' ? 'success.main' : 'error.main'}
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              {trend === 'up' ? '↑' : '↓'} {trendValue}
            </Typography>
            <Typography variant="subtitle2" color="textSecondary" sx={{ ml: 1 }}>
              vs last month
            </Typography>
          </Box>
        )}
      </Box>
      <Box
        sx={{
          backgroundColor: color,
          p: 1,
          borderRadius: 2,
          color: 'white',
          opacity: loading ? 0.5 : 1,
        }}
      >
        {icon}
      </Box>
    </Box>
  </Paper>
);

const SalesOrderStats: React.FC = () => {
  const [stats, setStats] = useState<SalesOrderStatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch sales orders and calculate stats
        const response = await api.get('/sales/sales-orders/', {
          params: { page_size: 1000 }
        });

        const orders = response.data.results || response.data || [];
        console.log('Sales Orders for stats:', orders);

        // Calculate statistics
        const statsData: SalesOrderStatsData = {
          total_orders: orders.length,
          draft_orders: orders.filter((o: any) => o.status === 'draft').length,
          pending_orders: orders.filter((o: any) => o.status === 'pending').length,
          sent_orders: orders.filter((o: any) => o.status === 'sent').length,
          acknowledged_orders: orders.filter((o: any) => o.status === 'acknowledged').length,
          delivered_orders: orders.filter((o: any) => o.status === 'delivered' || o.status === 'closed').length,
          cancelled_orders: orders.filter((o: any) => o.status === 'cancelled').length,
          total_value: orders.reduce((sum: number, o: any) => sum + (o.total_amount || 0), 0),
          pending_value: orders
            .filter((o: any) => ['pending', 'sent', 'acknowledged'].includes(o.status))
            .reduce((sum: number, o: any) => sum + (o.total_amount || 0), 0),
          delivered_value: orders
            .filter((o: any) => ['delivered', 'closed'].includes(o.status))
            .reduce((sum: number, o: any) => sum + (o.total_amount || 0), 0),
          average_order_value: orders.length > 0 
            ? orders.reduce((sum: number, o: any) => sum + (o.total_amount || 0), 0) / orders.length 
            : 0,
        };

        setStats(statsData);
      } catch (err) {
        console.error('Failed to fetch sales order stats:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const formatCurrency = (amount: number) => {
    const symbol = currencyInfo?.functional_currency_symbol || '$';
    return `${symbol}${amount.toLocaleString(undefined, { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    })}`;
  };

  if (error) {
    return (
      <Paper sx={{ p: 2, borderRadius: 2 }}>
        <Typography color="error">Error loading statistics: {error}</Typography>
      </Paper>
    );
  }

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Total Orders"
          value={stats?.total_orders.toString() || '0'}
          icon={<Assignment />}
          color="#1976d2"
          loading={loading}
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Total Value"
          value={stats ? formatCurrency(stats.total_value) : '$0.00'}
          icon={<AttachMoney />}
          color="#2e7d32"
          loading={loading}
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Pending Orders"
          value={stats?.pending_orders.toString() || '0'}
          icon={<Schedule />}
          color="#ed6c02"
          loading={loading}
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Delivered Orders"
          value={stats?.delivered_orders.toString() || '0'}
          icon={<CheckCircle />}
          color="#2e7d32"
          loading={loading}
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Draft Orders"
          value={stats?.draft_orders.toString() || '0'}
          icon={<Receipt />}
          color="#757575"
          loading={loading}
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Pending Value"
          value={stats ? formatCurrency(stats.pending_value) : '$0.00'}
          icon={<TrendingUp />}
          color="#ed6c02"
          loading={loading}
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Average Order"
          value={stats ? formatCurrency(stats.average_order_value) : '$0.00'}
          icon={<LocalShipping />}
          color="#1976d2"
          loading={loading}
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={3}>
        <StatCard
          title="Cancelled Orders"
          value={stats?.cancelled_orders.toString() || '0'}
          icon={<Cancel />}
          color="#d32f2f"
          loading={loading}
        />
      </Grid>
    </Grid>
  );
};

export default SalesOrderStats;
