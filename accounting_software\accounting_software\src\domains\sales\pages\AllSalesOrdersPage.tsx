import React from 'react';
import { 
  Container, Typography, Paper, Box, Grid, 
  Card, CardContent, IconButton, Tooltip 
} from '@mui/material';
import { 
  TrendingUp, AttachMoney, Receipt, 
  ShowChart, Download as DownloadIcon 
} from '@mui/icons-material';
import StatCard from '../../../shared/components/StatCard';
import RecentSalesOrders from '../components/RecentSalesOrders';
import SalesOrderChart from '../components/SalesOrderChart';
import SalesOrderStats from '../components/SalesOrderStats';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';

const AllSalesOrdersPage: React.FC = () => {
  // Mock data for analytics
  const salesStats = {
    totalSales: 287000,
    monthlyGrowth: 15.2,
    averageOrderValue: 1850,
    pendingOrders: 12,
  };

  const monthlyData = [
    { month: 'Jan', sales: 22200 },
    { month: 'Feb', sales: 24800 },
    { month: 'Mar', sales: 26200 },
    { month: 'Apr', sales: 28500 },
    { month: 'May', sales: 31700 },
    { month: 'Jun', sales: 33300 },
  ];

  return (
    <PageContainer title="Sales Overview">
      <Grid container spacing={3}>
        {/* Enhanced Stats Cards with Real Data */}
        <Grid item xs={12}>
          <SalesOrderStats />
        </Grid>
        
        {/* Charts */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6">Sales Trends</Typography>
              <Tooltip title="Download Report">
                <IconButton size="small">
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </Box>
            <SalesOrderChart data={monthlyData} />
          </Paper>
        </Grid>
        
        {/* Recent Sales Orders */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6">Recent Sales Orders</Typography>
              <Tooltip title="View All">
                <IconButton size="small" onClick={() => window.location.href = '/dashboard/sales/orders'}>
                  <ShowChart />
                </IconButton>
              </Tooltip>
            </Box>
            <RecentSalesOrders />
          </Paper>
        </Grid>
      </Grid>
    </PageContainer>
  );
};

export default AllSalesOrdersPage;
