import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Snackbar,
  LinearProgress,
} from '@mui/material';
import {
  Receipt as InvoiceIcon,
  LocalShipping as DeliveryIcon,
  Inventory as StockIcon,
  Timeline as WorkflowIcon,
  Assessment as ReportIcon,
  Print as PrintIcon,
} from '@mui/icons-material';

import OdooSalesOrderForm from '../components/OdooSalesOrderForm';
import { 
  OdooSalesOrder, 
  SalesOrderState,
  odooSalesOrderService 
} from '../../../services/odoo-sales-order.service';
import { stockIntegrationService } from '../../../services/stock-integration.service';
import { invoicePolicyService } from '../../../services/invoice-policy.service';

const OdooSalesOrderPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  // State
  const [salesOrder, setSalesOrder] = useState<OdooSalesOrder | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Dialog states
  const [confirmDialog, setConfirmDialog] = useState(false);
  const [invoiceDialog, setInvoiceDialog] = useState(false);
  const [deliveryDialog, setDeliveryDialog] = useState(false);
  
  // Workflow states
  const [confirmationResult, setConfirmationResult] = useState<any>(null);
  const [invoicePreview, setInvoicePreview] = useState<any>(null);
  const [deliveryStatus, setDeliveryStatus] = useState<any>(null);

  useEffect(() => {
    if (isEdit) {
      loadSalesOrder();
      loadAdditionalData();
    }
  }, [id, isEdit]);

  const loadSalesOrder = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const data = await odooSalesOrderService.getSalesOrder(parseInt(id));
      setSalesOrder(data);
    } catch (err: any) {
      setError(err.message || 'Failed to load sales order');
    } finally {
      setLoading(false);
    }
  };

  const loadAdditionalData = async () => {
    if (!id) return;
    
    try {
      // Load delivery status
      const deliveryStatusData = await stockIntegrationService.getDeliveryStatus(parseInt(id));
      setDeliveryStatus(deliveryStatusData);
    } catch (err) {
      console.error('Error loading additional data:', err);
    }
  };

  const handleConfirmOrder = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const result = await stockIntegrationService.processSalesOrderConfirmation(parseInt(id));
      setConfirmationResult(result);
      
      if (result.success) {
        setSuccess('Sales order confirmed successfully');
        setSalesOrder(result.sales_order);
        setDeliveryStatus(await stockIntegrationService.getDeliveryStatus(parseInt(id)));
      } else {
        setError(result.errors.join(', '));
      }
    } catch (err: any) {
      setError(err.message || 'Failed to confirm sales order');
    } finally {
      setLoading(false);
      setConfirmDialog(false);
    }
  };

  const handleCreateInvoice = async () => {
    if (!id || !salesOrder) return;
    
    try {
      setLoading(true);
      const preview = await invoicePolicyService.previewInvoice(parseInt(id), {
        invoice_policy: salesOrder.invoice_policy,
      });
      setInvoicePreview(preview);
      
      if (preview.can_create_invoice) {
        const result = await invoicePolicyService.createInvoiceFromSalesOrder(parseInt(id), {
          invoice_policy: salesOrder.invoice_policy,
        });
        
        setSuccess('Invoice created successfully');
        setSalesOrder(result.updated_sales_order);
      } else {
        setError('Cannot create invoice: ' + preview.warnings.join(', '));
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create invoice');
    } finally {
      setLoading(false);
      setInvoiceDialog(false);
    }
  };

  const getStateColor = (state: SalesOrderState) => {
    const colors = {
      draft: 'default' as const,
      sent: 'info' as const,
      sale: 'primary' as const,
      done: 'success' as const,
      cancel: 'error' as const,
    };
    return colors[state] || 'default';
  };

  const getProgressValue = () => {
    if (!salesOrder) return 0;
    
    const states = ['draft', 'sent', 'sale', 'done'];
    const currentIndex = states.indexOf(salesOrder.state);
    return ((currentIndex + 1) / states.length) * 100;
  };

  const renderWorkflowProgress = () => {
    if (!salesOrder) return null;
    
    const steps = [
      { key: 'draft', label: 'Quotation', icon: <WorkflowIcon /> },
      { key: 'sent', label: 'Sent', icon: <WorkflowIcon /> },
      { key: 'sale', label: 'Confirmed', icon: <WorkflowIcon /> },
      { key: 'done', label: 'Done', icon: <WorkflowIcon /> },
    ];
    
    const currentIndex = steps.findIndex(step => step.key === salesOrder.state);
    
    return (
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Progress
          </Typography>
          <Box sx={{ mb: 2 }}>
            <LinearProgress 
              variant="determinate" 
              value={getProgressValue()} 
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>
          <Grid container spacing={2}>
            {steps.map((step, index) => (
              <Grid item xs={3} key={step.key}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    opacity: index <= currentIndex ? 1 : 0.5,
                  }}
                >
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      bgcolor: index <= currentIndex ? 'primary.main' : 'grey.300',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      mb: 1,
                    }}
                  >
                    {step.icon}
                  </Box>
                  <Typography variant="caption" textAlign="center">
                    {step.label}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>
    );
  };

  const renderSmartButtons = () => {
    if (!salesOrder || !isEdit) return null;
    
    const buttons = [];
    
    // Delivery button
    if (salesOrder.delivery_count > 0) {
      buttons.push(
        <Card key="delivery" sx={{ cursor: 'pointer', minWidth: 120 }}>
          <CardContent 
            sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center', 
              py: 2,
              '&:hover': { bgcolor: 'action.hover' }
            }}
            onClick={() => navigate(`/dashboard/sales/orders/${id}/deliveries`)}
          >
            <DeliveryIcon color="primary" sx={{ mb: 1 }} />
            <Typography variant="h6">{salesOrder.delivery_count}</Typography>
            <Typography variant="caption">Delivery</Typography>
          </CardContent>
        </Card>
      );
    }
    
    // Invoice button
    if (salesOrder.invoice_count > 0) {
      buttons.push(
        <Card key="invoice" sx={{ cursor: 'pointer', minWidth: 120 }}>
          <CardContent 
            sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center', 
              py: 2,
              '&:hover': { bgcolor: 'action.hover' }
            }}
            onClick={() => navigate(`/dashboard/sales/orders/${id}/invoices`)}
          >
            <InvoiceIcon color="primary" sx={{ mb: 1 }} />
            <Typography variant="h6">{salesOrder.invoice_count}</Typography>
            <Typography variant="caption">Invoice</Typography>
          </CardContent>
        </Card>
      );
    }
    
    return buttons.length > 0 ? (
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        {buttons}
      </Box>
    ) : null;
  };

  const renderActionButtons = () => {
    if (!salesOrder || !isEdit) return null;
    
    const buttons = [];
    
    if (salesOrder.state === 'draft' || salesOrder.state === 'sent') {
      buttons.push(
        <Button
          key="confirm"
          variant="contained"
          onClick={() => setConfirmDialog(true)}
          disabled={loading}
          sx={{ bgcolor: '#875a7b', '&:hover': { bgcolor: '#6f4c69' } }}
        >
          Confirm Order
        </Button>
      );
    }
    
    if (salesOrder.state === 'sale') {
      const canInvoice = invoicePolicyService.canCreateInvoice(salesOrder);
      if (canInvoice.can_invoice) {
        buttons.push(
          <Button
            key="invoice"
            variant="contained"
            startIcon={<InvoiceIcon />}
            onClick={() => setInvoiceDialog(true)}
            disabled={loading}
          >
            Create Invoice
          </Button>
        );
      }
    }
    
    buttons.push(
      <Button
        key="print"
        variant="outlined"
        startIcon={<PrintIcon />}
        onClick={() => window.print()}
      >
        Print
      </Button>
    );
    
    return buttons.length > 0 ? (
      <Box sx={{ display: 'flex', gap: 2, mb: 3, justifyContent: 'flex-end' }}>
        {buttons}
      </Box>
    ) : null;
  };

  if (!isEdit) {
    return <OdooSalesOrderForm mode="create" />;
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4">
            {salesOrder?.name || 'Loading...'}
          </Typography>
          {salesOrder && (
            <Chip
              label={odooSalesOrderService.getStateLabel(salesOrder.state)}
              color={getStateColor(salesOrder.state)}
              sx={{ mt: 1 }}
            />
          )}
        </Box>
        <Button
          variant="outlined"
          onClick={() => navigate('/dashboard/sales/orders')}
        >
          Back to Orders
        </Button>
      </Box>

      {/* Workflow Progress */}
      {renderWorkflowProgress()}

      {/* Smart Buttons */}
      {renderSmartButtons()}

      {/* Action Buttons */}
      {renderActionButtons()}

      {/* Main Form */}
      <OdooSalesOrderForm mode="edit" />

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialog} onClose={() => setConfirmDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Confirm Sales Order</DialogTitle>
        <DialogContent>
          <Typography gutterBottom>
            This will confirm the sales order and trigger the following actions:
          </Typography>
          <ul>
            <li>Reserve stock for ordered products</li>
            <li>Create delivery orders based on delivery policy</li>
            <li>Update order state to "Sales Order"</li>
            {salesOrder?.invoice_policy === 'order' && (
              <li>Create invoice for ordered quantities</li>
            )}
          </ul>
          <Alert severity="info" sx={{ mt: 2 }}>
            Once confirmed, the order cannot be modified without cancellation.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleConfirmOrder} 
            variant="contained"
            disabled={loading}
          >
            Confirm Order
          </Button>
        </DialogActions>
      </Dialog>

      {/* Invoice Dialog */}
      <Dialog open={invoiceDialog} onClose={() => setInvoiceDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Invoice</DialogTitle>
        <DialogContent>
          {invoicePreview && (
            <Box>
              <Typography gutterBottom>
                Invoice will be created with the following details:
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  Policy: {salesOrder?.invoice_policy === 'order' ? 'Ordered quantities' : 'Delivered quantities'}
                </Typography>
                <Typography variant="body2">
                  Lines: {invoicePreview.lines.length}
                </Typography>
                <Typography variant="body2">
                  Subtotal: ${invoicePreview.subtotal.toFixed(2)}
                </Typography>
                <Typography variant="body2">
                  Total: ${invoicePreview.total_amount.toFixed(2)}
                </Typography>
              </Box>
              {invoicePreview.warnings.length > 0 && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  {invoicePreview.warnings.join(', ')}
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setInvoiceDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateInvoice} 
            variant="contained"
            disabled={loading || (invoicePreview && !invoicePreview.can_create_invoice)}
          >
            Create Invoice
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbars */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>

      <Snackbar
        open={!!success}
        autoHideDuration={4000}
        onClose={() => setSuccess(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default OdooSalesOrderPage;
