/**
 * General Ledger API Service
 * Handles all API calls for the GL module
 */

import { Account, AccountType, DetailType, JournalEntry, GLApiResponse } from '../shared/types/gl.types';

// Create a separate API instance for GL endpoints to handle authentication correctly
const glApi = {
  async get(url: string) {
    const token = localStorage.getItem('token');
    console.log('GL API GET request:', { url: `http://localhost:8000/api${url}`, hasToken: !!token });
    
    const response = await fetch(`http://localhost:8000/api${url}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      }
    });
    
    console.log('GL API GET response:', { status: response.status, statusText: response.statusText, url });
    
    if (!response.ok) {
      let errorText = 'Failed to read error response';
      try {
        const errorData = await response.json();
        errorText = JSON.stringify(errorData);
      } catch {
        errorText = await response.text().catch(() => 'Failed to read error response');
      }
      console.error('GL API GET error:', { status: response.status, statusText: response.statusText, errorText });
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    const data = await response.json();
    console.log('GL API GET success:', { url, dataKeys: Object.keys(data) });
    return { data };
  },
  
  async post(url: string, data: any) {
    const token = localStorage.getItem('token');
    console.log('GL API POST request:', { url: `http://localhost:8000/api${url}`, data });
    
    const response = await fetch(`http://localhost:8000/api${url}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      let errorText = 'Failed to read error response';
      try {
        const errorData = await response.json();
        errorText = JSON.stringify(errorData);
      } catch {
        errorText = await response.text().catch(() => 'Failed to read error response');
      }
      console.error('GL API POST error:', { status: response.status, statusText: response.statusText, errorText, sentData: data });
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    return { data: await response.json() };
  },
  
  async put(url: string, data: any) {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:8000/api${url}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      },
      body: JSON.stringify(data)
    });
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return { data: await response.json() };
  },
  
  async delete(url: string) {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:8000/api${url}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      }
    });
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }
};

// API Service Classes using authenticated fetch
class GLApiService<T> {
  constructor(private endpoint: string) {}

  async list(): Promise<GLApiResponse<T>> {
    const response = await glApi.get(`/${this.endpoint}/`);
    return response.data;
  }

  async get(id: number): Promise<T> {
    const response = await glApi.get(`/${this.endpoint}/${id}/`);
    return response.data;
  }

  async create(data: Partial<T>): Promise<T> {
    const response = await glApi.post(`/${this.endpoint}/`, data);
    return response.data;
  }

  async update(id: number, data: Partial<T>): Promise<T> {
    const response = await glApi.put(`/${this.endpoint}/${id}/`, data);
    return response.data;
  }

  async partialUpdate(id: number, data: Partial<T>): Promise<T> {
    // PATCH request for partial update
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:8000/api/${this.endpoint}/${id}/`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      },
      body: JSON.stringify(data)
    });
    if (!response.ok) {
      let errorText = 'Failed to read error response';
      try {
        const errorData = await response.json();
        errorText = JSON.stringify(errorData);
      } catch {
        errorText = await response.text().catch(() => 'Failed to read error response');
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    return await response.json();
  }

  async delete(id: number): Promise<void> {
    await glApi.delete(`/${this.endpoint}/${id}/`);
  }
}

// Specialized API services for paginated endpoints that we want as arrays
class SimpleApiService<T> {
  constructor(private endpoint: string) {}

  async list(): Promise<T[]> {
    const response = await glApi.get(`/${this.endpoint}/`);
    // Handle both paginated and non-paginated responses
    if (response.data && typeof response.data === 'object' && 'results' in response.data) {
      // Paginated response - return the results array
      return response.data.results;
    } else if (Array.isArray(response.data)) {
      // Direct array response
      return response.data;
    } else {
      // Unexpected format - return empty array to prevent errors
      console.warn(`Unexpected response format from ${this.endpoint}:`, response.data);
      return [];
    }
  }

  async get(id: number): Promise<T> {
    const response = await glApi.get(`/${this.endpoint}/${id}/`);
    return response.data;
  }

  async create(data: Partial<T>): Promise<T> {
    const response = await glApi.post(`/${this.endpoint}/`, data);
    return response.data;
  }

  async update(id: number, data: Partial<T>): Promise<T> {
    const response = await glApi.put(`/${this.endpoint}/${id}/`, data);
    return response.data;
  }

  async partialUpdate(id: number, data: Partial<T>): Promise<T> {
    // PATCH request for partial update
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:8000/api/${this.endpoint}/${id}/`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      },
      body: JSON.stringify(data)
    });
    if (!response.ok) {
      let errorText = 'Failed to read error response';
      try {
        const errorData = await response.json();
        errorText = JSON.stringify(errorData);
      } catch {
        errorText = await response.text().catch(() => 'Failed to read error response');
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    return await response.json();
  }

  async delete(id: number): Promise<void> {
    await glApi.delete(`/${this.endpoint}/${id}/`);
  }
}

// Export API service instances
export const accountsApi = new GLApiService<Account>('gl/accounts');
export const accountTypesApi = new SimpleApiService<AccountType>('gl/account-types');
export const detailTypesApi = new SimpleApiService<DetailType>('gl/detail-types');
export const journalEntriesApi = new GLApiService<JournalEntry>('gl/journal-entries');

// Utility Functions
export const formatCurrency = (amount: string | number, currency: string = 'USD'): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(numAmount);
};

export const getAccountTypeColor = (accountType: string): string => {
  const colors: Record<string, string> = {
    'Asset': '#4caf50',
    'Liability': '#f44336',
    'Equity': '#2196f3',
    'Revenue': '#ff9800',
    'Expense': '#9c27b0',
    'Other Income': '#00bcd4',
    'Other Expense': '#795548',
  };
  return colors[accountType] || '#757575';
};

export function canEditAccount(user: any): boolean {
  return user?.is_superuser || user?.is_staff || false;
}

// Fast Chart of Accounts loader - single optimized API call
export const loadChartOfAccountsFast = async () => {
  const token = localStorage.getItem('token');
  console.log('Fast COA: Starting optimized Chart of Accounts load...');
  
  const startTime = performance.now();
  
  const response = await fetch('http://localhost:8000/api/gl/chart-of-accounts-fast/', {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Token ${token}` })
    }
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
  }
  
  const data = await response.json();
  const endTime = performance.now();
  const clientTime = Math.round(endTime - startTime);
  
  console.log('Fast COA: Loaded successfully', {
    serverTime: data.metadata.load_time_ms,
    clientTime: clientTime,
    totalAccounts: data.metadata.total_accounts,
    activeAccounts: data.metadata.active_accounts,
    transactionAccounts: data.metadata.transaction_accounts,
    databaseQueries: data.metadata.database_queries
  });
  
  return data;
};

// Financial Statements API
export interface FinancialStatementsParams {
  statement_type?: 'balance_sheet' | 'income_statement' | 'cash_flow' | 'all';
  period_type?: 'fiscal_year' | 'calendar_year' | 'custom';
  year?: number;
  from_date?: string;
  to_date?: string;
  currency?: 'functional' | 'reporting' | string;
  include_comparatives?: boolean;
}

export interface BalanceSheetAccount {
  account_id: number;
  account_number: string;
  account_name: string;
  account_type: string;
  detail_type: string;
  balance_sheet_classification: 'CURRENT' | 'NON_CURRENT' | '';
  balance: number;
  comparative_balance?: number;
  variance?: number;
  variance_percent?: number;
  // Virtual account fields
  is_virtual?: boolean;
  virtual_type?: 'profit_for_year';
}

export interface BalanceSheetData {
  as_of_date: string;
  comparative_date?: string;
  // Professional Balance Sheet Structure
  current_assets: BalanceSheetAccount[];
  non_current_assets: BalanceSheetAccount[];
  current_liabilities: BalanceSheetAccount[];
  non_current_liabilities: BalanceSheetAccount[];
  equity: BalanceSheetAccount[];
  // Detailed totals
  total_current_assets: number;
  total_non_current_assets: number;
  total_current_liabilities: number;
  total_non_current_liabilities: number;
  total_equity: number;
  // Summary totals
  total_assets: number;
  total_liabilities: number;
  total_liabilities_and_equity: number;
  working_capital: number;
  is_balanced: boolean;
  // Net Income Information
  current_year_net_income: number;
  fiscal_year_start: string;
  // Legacy fields for backward compatibility
  assets: BalanceSheetAccount[];
  liabilities: BalanceSheetAccount[];
}

export interface IncomeStatementAccount {
  account_id: number;
  account_number: string;
  account_name: string;
  account_type: string;
  period_total: number;
  comparative_total?: number;
  variance?: number;
  variance_percent?: number;
}

export interface IncomeStatementData {
  period_start: string;
  period_end: string;
  comparative_start?: string;
  comparative_end?: string;
  revenue: IncomeStatementAccount[];
  expenses: IncomeStatementAccount[];
  total_revenue: number;
  total_expenses: number;
  net_income: number;
  gross_profit_margin: number;
}

export interface CashFlowAccount {
  account_id: number;
  account_number: string;
  account_name: string;
  cash_inflows: number;
  cash_outflows: number;
  net_cash_flow: number;
}

export interface CashFlowData {
  period_start: string;
  period_end: string;
  cash_accounts: CashFlowAccount[];
  total_cash_flow: number;
  note: string;
}

export interface FinancialStatementsResponse {
  company_name: string;
  period_type: string;
  period_start: string;
  period_end: string;
  currency: string;
  include_comparatives: boolean;
  generated_at: string;
  comparative_start?: string;
  comparative_end?: string;
  balance_sheet?: BalanceSheetData;
  income_statement?: IncomeStatementData;
  cash_flow?: CashFlowData;
}

export const loadFinancialStatements = async (params: FinancialStatementsParams = {}): Promise<FinancialStatementsResponse> => {
  console.log('GL Service: Loading financial statements with params:', params);
  
  try {
    const queryParams = new URLSearchParams();
    
    if (params.statement_type) queryParams.append('statement_type', params.statement_type);
    if (params.period_type) queryParams.append('period_type', params.period_type);
    if (params.year) queryParams.append('year', params.year.toString());
    if (params.from_date) queryParams.append('from_date', params.from_date);
    if (params.to_date) queryParams.append('to_date', params.to_date);
    if (params.currency) queryParams.append('currency', params.currency);
    if (params.include_comparatives !== undefined) {
      queryParams.append('include_comparatives', params.include_comparatives.toString());
    }
    
    const url = `/gl/financial-statements/?${queryParams.toString()}`;
    console.log('GL Service: Making API call to:', url);
    
    const startTime = performance.now();
    const response = await glApi.get(url);
    const endTime = performance.now();
    
    console.log('GL Service: Financial statements loaded successfully');
    console.log(`GL Service: API call took ${(endTime - startTime).toFixed(2)}ms`);
    console.log('GL Service: Response data:', response);
    
    return response.data;
  } catch (error) {
    console.error('GL Service: Error loading financial statements:', error);
    throw error;
  }
};

// Odoo-style AccountMove Service
class AccountMoveGLService {
  private baseUrl = '/gl';

  async getAccountMoves(filters?: any): Promise<{ results: any[]; count: number }> {
    const params = new URLSearchParams();

    if (filters?.move_type) params.append('move_type', filters.move_type);
    if (filters?.state) params.append('state', filters.state);
    if (filters?.partner_id) params.append('partner_id', filters.partner_id.toString());
    if (filters?.journal_id) params.append('journal_id', filters.journal_id.toString());
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.search) params.append('search', filters.search);

    const response = await glApi.get(`${this.baseUrl}/account-moves/?${params.toString()}`);
    return {
      results: response.data.results || [],
      count: response.data.count || 0
    };
  }

  async getAccountMove(id: number): Promise<any> {
    const response = await glApi.get(`${this.baseUrl}/account-moves/${id}/`);
    return response.data;
  }

  async createAccountMove(moveData: any): Promise<any> {
    const response = await glApi.post(`${this.baseUrl}/account-moves/`, moveData);
    return response.data;
  }

  async updateAccountMove(id: number, moveData: any): Promise<any> {
    const response = await glApi.patch(`${this.baseUrl}/account-moves/${id}/`, moveData);
    return response.data;
  }

  async postAccountMove(id: number): Promise<any> {
    const response = await glApi.post(`${this.baseUrl}/account-moves/${id}/action_post/`);
    return response.data;
  }

  async cancelAccountMove(id: number): Promise<any> {
    const response = await glApi.post(`${this.baseUrl}/account-moves/${id}/button_cancel/`);
    return response.data;
  }

  async getAccountMoveStats(): Promise<any> {
    const response = await glApi.get(`${this.baseUrl}/account-moves/stats/`);
    return response.data;
  }

  // Journal management
  async getJournals(): Promise<any[]> {
    const response = await glApi.get(`${this.baseUrl}/journals/`);
    return response.data.results || response.data;
  }

  async createJournal(journalData: any): Promise<any> {
    const response = await glApi.post(`${this.baseUrl}/journals/`, journalData);
    return response.data;
  }

  // Sequence management
  async getSequences(): Promise<any[]> {
    const response = await glApi.get(`${this.baseUrl}/sequences/`);
    return response.data.results || response.data;
  }

  async createSequence(sequenceData: any): Promise<any> {
    const response = await glApi.post(`${this.baseUrl}/sequences/`, sequenceData);
    return response.data;
  }
}

export const accountMoveGLService = new AccountMoveGLService();