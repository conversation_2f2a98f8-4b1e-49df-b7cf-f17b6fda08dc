import React from 'react';
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  Autocomplete,
  Box,
  Typography,
} from '@mui/material';
import StandardDatePicker from './StandardDatePicker';
import dayjs from 'dayjs';

interface BaseFieldProps {
  label: string;
  required?: boolean;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
}

interface OdooTextFieldProps extends BaseFieldProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  multiline?: boolean;
  rows?: number;
  type?: 'text' | 'number' | 'email' | 'password';
}

export const OdooTextField: React.FC<OdooTextFieldProps> = ({
  label,
  value,
  onChange,
  required = false,
  disabled = false,
  error = false,
  helperText,
  placeholder,
  multiline = false,
  rows = 1,
  type = 'text',
  fullWidth = true,
}) => {
  return (
    <Box>
      <Typography
        variant="body2"
        sx={{
          fontWeight: 500,
          color: '#495057',
          mb: 1,
          fontSize: '0.875rem',
        }}
      >
        {label}
        {required && (
          <span style={{ color: '#dc3545', marginLeft: 4 }}>*</span>
        )}
      </Typography>
      <TextField
        fullWidth={fullWidth}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        error={error}
        helperText={helperText}
        placeholder={placeholder}
        multiline={multiline}
        rows={rows}
        type={type}
        size="small"
        sx={{
          '& .MuiOutlinedInput-notchedOutline': {
            border: '1px solid #ced4da',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: '#875a7b',
          },
          '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: '#875a7b',
          },
          '& .MuiInputBase-input': {
            fontSize: '0.875rem',
          },
        }}
      />
    </Box>
  );
};

interface SelectOption {
  value: string | number;
  label: string;
}

interface OdooSelectFieldProps extends BaseFieldProps {
  value: string | number;
  onChange: (value: string | number) => void;
  options: SelectOption[];
  placeholder?: string;
}

export const OdooSelectField: React.FC<OdooSelectFieldProps> = ({
  label,
  value,
  onChange,
  options,
  required = false,
  disabled = false,
  error = false,
  helperText,
  placeholder = 'Select an option',
  fullWidth = true,
}) => {
  return (
    <Box>
      <Typography
        variant="body2"
        sx={{
          fontWeight: 500,
          color: '#495057',
          mb: 1,
          fontSize: '0.875rem',
        }}
      >
        {label}
        {required && (
          <span style={{ color: '#dc3545', marginLeft: 4 }}>*</span>
        )}
      </Typography>
      <FormControl fullWidth={fullWidth} size="small" error={error}>
        <Select
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          disabled={disabled}
          displayEmpty
          sx={{
            '& .MuiOutlinedInput-notchedOutline': {
              border: '1px solid #ced4da',
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#875a7b',
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: '#875a7b',
            },
            '& .MuiSelect-select': {
              fontSize: '0.875rem',
            },
          }}
        >
          <MenuItem value="">
            <em>{placeholder}</em>
          </MenuItem>
          {options.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
        {helperText && (
          <Typography
            variant="caption"
            sx={{
              color: error ? '#dc3545' : '#6c757d',
              mt: 0.5,
              fontSize: '0.75rem',
            }}
          >
            {helperText}
          </Typography>
        )}
      </FormControl>
    </Box>
  );
};

interface OdooDateFieldProps extends BaseFieldProps {
  value: string;
  onChange: (value: string) => void;
}

export const OdooDateField: React.FC<OdooDateFieldProps> = ({
  label,
  value,
  onChange,
  required = false,
  disabled = false,
  error = false,
  helperText,
}) => {
  return (
    <Box>
      <Typography
        variant="body2"
        sx={{
          fontWeight: 500,
          color: '#495057',
          mb: 1,
          fontSize: '0.875rem',
        }}
      >
        {label}
        {required && (
          <span style={{ color: '#dc3545', marginLeft: 4 }}>*</span>
        )}
      </Typography>
      <StandardDatePicker
        value={dayjs(value)}
        onChange={(newValue: any) => onChange(newValue?.format('YYYY-MM-DD') || '')}
        disabled={disabled}
        slotProps={{
          textField: {
            size: 'small',
            error,
            helperText,
            sx: {
              '& .MuiOutlinedInput-notchedOutline': {
                border: '1px solid #ced4da',
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#875a7b',
              },
              '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#875a7b',
              },
              '& .MuiInputBase-input': {
                fontSize: '0.875rem',
              },
            },
          },
        }}
      />
    </Box>
  );
};

interface AutocompleteOption {
  id: string | number;
  label: string;
}

interface OdooAutocompleteFieldProps extends BaseFieldProps {
  value: AutocompleteOption | null;
  onChange: (value: AutocompleteOption | null) => void;
  options: AutocompleteOption[];
  placeholder?: string;
  loading?: boolean;
}

export const OdooAutocompleteField: React.FC<OdooAutocompleteFieldProps> = ({
  label,
  value,
  onChange,
  options,
  required = false,
  disabled = false,
  error = false,
  helperText,
  placeholder = 'Search and select',
  loading = false,
  fullWidth = true,
}) => {
  return (
    <Box>
      <Typography
        variant="body2"
        sx={{
          fontWeight: 500,
          color: '#495057',
          mb: 1,
          fontSize: '0.875rem',
        }}
      >
        {label}
        {required && (
          <span style={{ color: '#dc3545', marginLeft: 4 }}>*</span>
        )}
      </Typography>
      <Autocomplete
        value={value}
        onChange={(_, newValue) => onChange(newValue)}
        options={options}
        getOptionLabel={(option) => option.label}
        isOptionEqualToValue={(option, value) => option.id === value.id}
        disabled={disabled}
        loading={loading}
        size="small"
        fullWidth={fullWidth}
        renderInput={(params) => (
          <TextField
            {...params}
            placeholder={placeholder}
            error={error}
            helperText={helperText}
            sx={{
              '& .MuiOutlinedInput-notchedOutline': {
                border: '1px solid #ced4da',
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#875a7b',
              },
              '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#875a7b',
              },
              '& .MuiInputBase-input': {
                fontSize: '0.875rem',
              },
            }}
          />
        )}
      />
    </Box>
  );
};

export default {
  OdooTextField,
  OdooSelectField,
  OdooDateField,
  OdooAutocompleteField,
};
