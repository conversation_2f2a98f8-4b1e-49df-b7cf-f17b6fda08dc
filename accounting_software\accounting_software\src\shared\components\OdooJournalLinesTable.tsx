import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Select,
  MenuItem,
  FormControl,
  IconButton,
  Box,
  Typography,
  Button,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
// import { AccountMoveLine } from '../types/gl.types';

// Temporary interface to avoid import issues
interface AccountMoveLine {
  id?: number;
  account_id?: number;
  name?: string;
  debit?: string;
  credit?: string;
}

interface Account {
  id: number;
  account_number: string;
  account_name: string;
}

interface OdooJournalLinesTableProps {
  lines: Partial<AccountMoveLine>[];
  accounts: Account[];
  onLineChange: (index: number, field: keyof AccountMoveLine, value: any) => void;
  onAddLine: () => void;
  onRemoveLine: (index: number) => void;
  disabled?: boolean;
  minLines?: number;
}

export const OdooJournalLinesTable: React.FC<OdooJournalLinesTableProps> = ({
  lines,
  accounts,
  onLineChange,
  onAddLine,
  onRemoveLine,
  disabled = false,
  minLines = 2,
}) => {
  const formatCurrency = (value: string | number) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(num || 0);
  };

  const calculateTotals = () => {
    const totalDebit = lines.reduce((sum, line) => sum + parseFloat(line.debit || '0'), 0);
    const totalCredit = lines.reduce((sum, line) => sum + parseFloat(line.credit || '0'), 0);
    return { totalDebit, totalCredit };
  };

  const { totalDebit, totalCredit } = calculateTotals();
  const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01;

  return (
    <Box>
      <TableContainer
        sx={{
          border: '1px solid #dee2e6',
          borderRadius: '8px',
          bgcolor: 'white',
        }}
      >
        <Table size="small">
          <TableHead>
            <TableRow sx={{ bgcolor: '#f8f9fa' }}>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '35%',
                }}
              >
                Account
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '30%',
                }}
              >
                Label
              </TableCell>
              <TableCell
                align="right"
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '15%',
                }}
              >
                Debit
              </TableCell>
              <TableCell
                align="right"
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '15%',
                }}
              >
                Credit
              </TableCell>
              <TableCell
                align="center"
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '5%',
                }}
              >
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {lines.map((line, index) => (
              <TableRow
                key={index}
                sx={{
                  '&:hover': { bgcolor: '#f8f9fa' },
                  '&:last-child td': { borderBottom: 0 },
                }}
              >
                <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                  <FormControl fullWidth size="small">
                    <Select
                      value={line.account_id || ''}
                      onChange={(e) => onLineChange(index, 'account_id', e.target.value)}
                      disabled={disabled}
                      displayEmpty
                      sx={{
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: '1px solid #ced4da',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#875a7b',
                        },
                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#875a7b',
                        },
                      }}
                    >
                      <MenuItem value="">
                        <em>Select Account</em>
                      </MenuItem>
                      {accounts.map((account) => (
                        <MenuItem key={account.id} value={account.id}>
                          {account.account_number} - {account.account_name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </TableCell>
                <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={line.name || ''}
                    onChange={(e) => onLineChange(index, 'name', e.target.value)}
                    disabled={disabled}
                    placeholder={`Journal Entry Line ${index + 1}`}
                    sx={{
                      '& .MuiOutlinedInput-notchedOutline': {
                        border: '1px solid #ced4da',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#875a7b',
                      },
                      '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#875a7b',
                      },
                    }}
                  />
                </TableCell>
                <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                  <TextField
                    fullWidth
                    size="small"
                    type="number"
                    value={line.debit || '0.00'}
                    onChange={(e) => onLineChange(index, 'debit', e.target.value)}
                    disabled={disabled}
                    inputProps={{ step: '0.01', min: '0' }}
                    sx={{
                      '& .MuiOutlinedInput-input': {
                        textAlign: 'right',
                      },
                      '& .MuiOutlinedInput-notchedOutline': {
                        border: '1px solid #ced4da',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#875a7b',
                      },
                      '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#875a7b',
                      },
                    }}
                  />
                </TableCell>
                <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                  <TextField
                    fullWidth
                    size="small"
                    type="number"
                    value={line.credit || '0.00'}
                    onChange={(e) => onLineChange(index, 'credit', e.target.value)}
                    disabled={disabled}
                    inputProps={{ step: '0.01', min: '0' }}
                    sx={{
                      '& .MuiOutlinedInput-input': {
                        textAlign: 'right',
                      },
                      '& .MuiOutlinedInput-notchedOutline': {
                        border: '1px solid #ced4da',
                      },
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#875a7b',
                      },
                      '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: '#875a7b',
                      },
                    }}
                  />
                </TableCell>
                <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                  <IconButton
                    size="small"
                    onClick={() => onRemoveLine(index)}
                    disabled={disabled || lines.length <= minLines}
                    sx={{
                      color: '#dc3545',
                      '&:hover': { bgcolor: '#f8d7da' },
                      '&.Mui-disabled': { color: '#6c757d' },
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add Line Button */}
      <Box sx={{ mt: 2, mb: 3 }}>
        <Button
          startIcon={<AddIcon />}
          onClick={onAddLine}
          disabled={disabled}
          sx={{
            color: '#875a7b',
            '&:hover': { bgcolor: '#f8f5f7' },
          }}
        >
          Add a line
        </Button>
      </Box>

      {/* Totals */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: 4,
          p: 2,
          bgcolor: '#f8f9fa',
          border: '1px solid #dee2e6',
          borderRadius: '8px',
        }}
      >
        <Box sx={{ textAlign: 'right' }}>
          <Typography variant="body2" sx={{ color: '#6c757d', fontSize: '0.875rem' }}>
            Total Debit
          </Typography>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: '#495057',
              fontSize: '1rem',
            }}
          >
            {formatCurrency(totalDebit)}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'right' }}>
          <Typography variant="body2" sx={{ color: '#6c757d', fontSize: '0.875rem' }}>
            Total Credit
          </Typography>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: '#495057',
              fontSize: '1rem',
            }}
          >
            {formatCurrency(totalCredit)}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'right' }}>
          <Typography variant="body2" sx={{ color: '#6c757d', fontSize: '0.875rem' }}>
            Difference
          </Typography>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: isBalanced ? '#28a745' : '#dc3545',
              fontSize: '1rem',
            }}
          >
            {formatCurrency(Math.abs(totalDebit - totalCredit))}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default OdooJournalLinesTable;
