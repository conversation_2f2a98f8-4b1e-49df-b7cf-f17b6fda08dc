import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Select,
  MenuItem,
  FormControl,
  IconButton,
  Box,
  Typography,
  Button,
  Autocomplete,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Inventory as StockIcon,
} from '@mui/icons-material';

interface Product {
  id: number;
  name: string;
  default_code?: string;
  list_price: number;
  uom_name?: string;
  qty_available?: number;
}

interface SalesOrderLine {
  id?: number;
  product_id: number;
  product_name?: string;
  name: string;
  product_uom_qty: number;
  qty_delivered: number;
  qty_invoiced: number;
  qty_to_invoice: number;
  price_unit: number;
  discount: number;
  price_subtotal: number;
  price_total: number;
  tax_id?: number[];
  product_uom?: string;
  sequence: number;
}

interface OdooSalesOrderLinesTableProps {
  lines: SalesOrderLine[];
  products: Product[];
  onLineChange: (index: number, field: keyof SalesOrderLine, value: any) => void;
  onAddLine: () => void;
  onRemoveLine: (index: number) => void;
  disabled?: boolean;
  showDelivered?: boolean;
  showInvoiced?: boolean;
  currency?: string;
}

export const OdooSalesOrderLinesTable: React.FC<OdooSalesOrderLinesTableProps> = ({
  lines,
  products,
  onLineChange,
  onAddLine,
  onRemoveLine,
  disabled = false,
  showDelivered = false,
  showInvoiced = false,
  currency = 'USD',
}) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(value || 0);
  };

  const handleProductChange = (index: number, product: Product | null) => {
    if (product) {
      onLineChange(index, 'product_id', product.id);
      onLineChange(index, 'product_name', product.name);
      onLineChange(index, 'name', product.name);
      onLineChange(index, 'price_unit', product.list_price);
      onLineChange(index, 'product_uom', product.uom_name || 'Units');
    }
  };

  const calculateTotals = () => {
    const subtotal = lines.reduce((sum, line) => sum + line.price_subtotal, 0);
    const tax = lines.reduce((sum, line) => sum + (line.price_total - line.price_subtotal), 0);
    const total = subtotal + tax;
    return { subtotal, tax, total };
  };

  const { subtotal, tax, total } = calculateTotals();

  const getDeliveryStatus = (line: SalesOrderLine) => {
    if (line.qty_delivered === 0) return { label: 'Nothing', color: 'default' as const };
    if (line.qty_delivered < line.product_uom_qty) return { label: 'Partially', color: 'warning' as const };
    return { label: 'Fully', color: 'success' as const };
  };

  const getInvoiceStatus = (line: SalesOrderLine) => {
    if (line.qty_invoiced === 0) return { label: 'Nothing', color: 'default' as const };
    if (line.qty_invoiced < line.product_uom_qty) return { label: 'Partially', color: 'warning' as const };
    return { label: 'Fully', color: 'success' as const };
  };

  return (
    <Box>
      <TableContainer
        sx={{
          border: '1px solid #dee2e6',
          borderRadius: '8px',
          bgcolor: 'white',
        }}
      >
        <Table size="small">
          <TableHead>
            <TableRow sx={{ bgcolor: '#f8f9fa' }}>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '25%',
                }}
              >
                Product
              </TableCell>
              <TableCell
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '20%',
                }}
              >
                Description
              </TableCell>
              <TableCell
                align="right"
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '10%',
                }}
              >
                Qty
              </TableCell>
              {showDelivered && (
                <TableCell
                  align="right"
                  sx={{
                    fontWeight: 600,
                    color: '#495057',
                    fontSize: '0.875rem',
                    borderBottom: '1px solid #dee2e6',
                    width: '10%',
                  }}
                >
                  Delivered
                </TableCell>
              )}
              {showInvoiced && (
                <TableCell
                  align="right"
                  sx={{
                    fontWeight: 600,
                    color: '#495057',
                    fontSize: '0.875rem',
                    borderBottom: '1px solid #dee2e6',
                    width: '10%',
                  }}
                >
                  Invoiced
                </TableCell>
              )}
              <TableCell
                align="right"
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '12%',
                }}
              >
                Unit Price
              </TableCell>
              <TableCell
                align="right"
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '8%',
                }}
              >
                Disc.%
              </TableCell>
              <TableCell
                align="right"
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '12%',
                }}
              >
                Subtotal
              </TableCell>
              <TableCell
                align="center"
                sx={{
                  fontWeight: 600,
                  color: '#495057',
                  fontSize: '0.875rem',
                  borderBottom: '1px solid #dee2e6',
                  width: '5%',
                }}
              >
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {lines.map((line, index) => {
              const selectedProduct = products.find(p => p.id === line.product_id);
              const deliveryStatus = getDeliveryStatus(line);
              const invoiceStatus = getInvoiceStatus(line);
              
              return (
                <TableRow
                  key={index}
                  sx={{
                    '&:hover': { bgcolor: '#f8f9fa' },
                    '&:last-child td': { borderBottom: 0 },
                  }}
                >
                  <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                    <Autocomplete
                      size="small"
                      value={selectedProduct || null}
                      onChange={(_, newValue) => handleProductChange(index, newValue)}
                      options={products}
                      getOptionLabel={(option) => `${option.default_code || ''} ${option.name}`.trim()}
                      disabled={disabled}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select product"
                          sx={{
                            '& .MuiOutlinedInput-notchedOutline': {
                              border: '1px solid #ced4da',
                            },
                            '&:hover .MuiOutlinedInput-notchedOutline': {
                              borderColor: '#875a7b',
                            },
                            '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                              borderColor: '#875a7b',
                            },
                          }}
                        />
                      )}
                      renderOption={(props, option) => (
                        <Box component="li" {...props}>
                          <Box>
                            <Typography variant="body2">
                              {option.default_code && `[${option.default_code}] `}{option.name}
                            </Typography>
                            <Box display="flex" alignItems="center" gap={1}>
                              <Typography variant="caption" color="text.secondary">
                                {formatCurrency(option.list_price)}
                              </Typography>
                              {option.qty_available !== undefined && (
                                <Chip
                                  icon={<StockIcon />}
                                  label={`${option.qty_available} in stock`}
                                  size="small"
                                  variant="outlined"
                                  color={option.qty_available > 0 ? 'success' : 'error'}
                                />
                              )}
                            </Box>
                          </Box>
                        </Box>
                      )}
                    />
                  </TableCell>
                  
                  <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                    <TextField
                      fullWidth
                      size="small"
                      value={line.name || ''}
                      onChange={(e) => onLineChange(index, 'name', e.target.value)}
                      disabled={disabled}
                      multiline
                      maxRows={3}
                      sx={{
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: '1px solid #ced4da',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#875a7b',
                        },
                        '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#875a7b',
                        },
                      }}
                    />
                  </TableCell>
                  
                  <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                    <TextField
                      fullWidth
                      size="small"
                      type="number"
                      value={line.product_uom_qty || 0}
                      onChange={(e) => onLineChange(index, 'product_uom_qty', parseFloat(e.target.value) || 0)}
                      disabled={disabled}
                      inputProps={{ step: '0.01', min: '0' }}
                      sx={{
                        '& .MuiOutlinedInput-input': {
                          textAlign: 'right',
                        },
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: '1px solid #ced4da',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#875a7b',
                        },
                        '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#875a7b',
                        },
                      }}
                    />
                  </TableCell>
                  
                  {showDelivered && (
                    <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                      <Box display="flex" flexDirection="column" alignItems="flex-end">
                        <Typography variant="body2" sx={{ textAlign: 'right' }}>
                          {line.qty_delivered || 0}
                        </Typography>
                        <Chip
                          label={deliveryStatus.label}
                          size="small"
                          color={deliveryStatus.color}
                          variant="outlined"
                        />
                      </Box>
                    </TableCell>
                  )}
                  
                  {showInvoiced && (
                    <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                      <Box display="flex" flexDirection="column" alignItems="flex-end">
                        <Typography variant="body2" sx={{ textAlign: 'right' }}>
                          {line.qty_invoiced || 0}
                        </Typography>
                        <Chip
                          label={invoiceStatus.label}
                          size="small"
                          color={invoiceStatus.color}
                          variant="outlined"
                        />
                      </Box>
                    </TableCell>
                  )}
                  
                  <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                    <TextField
                      fullWidth
                      size="small"
                      type="number"
                      value={line.price_unit || 0}
                      onChange={(e) => onLineChange(index, 'price_unit', parseFloat(e.target.value) || 0)}
                      disabled={disabled}
                      inputProps={{ step: '0.01', min: '0' }}
                      sx={{
                        '& .MuiOutlinedInput-input': {
                          textAlign: 'right',
                        },
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: '1px solid #ced4da',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#875a7b',
                        },
                        '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#875a7b',
                        },
                      }}
                    />
                  </TableCell>
                  
                  <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                    <TextField
                      fullWidth
                      size="small"
                      type="number"
                      value={line.discount || 0}
                      onChange={(e) => onLineChange(index, 'discount', parseFloat(e.target.value) || 0)}
                      disabled={disabled}
                      inputProps={{ step: '0.01', min: '0', max: '100' }}
                      sx={{
                        '& .MuiOutlinedInput-input': {
                          textAlign: 'right',
                        },
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: '1px solid #ced4da',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#875a7b',
                        },
                        '& .Mui-focused .MuiOutlinedInput-notchedOutline': {
                          borderColor: '#875a7b',
                        },
                      }}
                    />
                  </TableCell>
                  
                  <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        textAlign: 'right',
                        fontWeight: 500,
                        color: '#495057',
                      }}
                    >
                      {formatCurrency(line.price_subtotal)}
                    </Typography>
                  </TableCell>
                  
                  <TableCell sx={{ borderBottom: '1px solid #f1f3f4', py: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => onRemoveLine(index)}
                      disabled={disabled || lines.length <= 1}
                      sx={{
                        color: '#dc3545',
                        '&:hover': { bgcolor: '#f8d7da' },
                        '&.Mui-disabled': { color: '#6c757d' },
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add Line Button */}
      <Box sx={{ mt: 2, mb: 3 }}>
        <Button
          startIcon={<AddIcon />}
          onClick={onAddLine}
          disabled={disabled}
          sx={{
            color: '#875a7b',
            '&:hover': { bgcolor: '#f8f5f7' },
          }}
        >
          Add a line
        </Button>
      </Box>

      {/* Totals */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: 4,
          p: 2,
          bgcolor: '#f8f9fa',
          border: '1px solid #dee2e6',
          borderRadius: '8px',
        }}
      >
        <Box sx={{ textAlign: 'right' }}>
          <Typography variant="body2" sx={{ color: '#6c757d', fontSize: '0.875rem' }}>
            Untaxed Amount
          </Typography>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: '#495057',
              fontSize: '1rem',
            }}
          >
            {formatCurrency(subtotal)}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'right' }}>
          <Typography variant="body2" sx={{ color: '#6c757d', fontSize: '0.875rem' }}>
            Taxes
          </Typography>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: '#495057',
              fontSize: '1rem',
            }}
          >
            {formatCurrency(tax)}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'right' }}>
          <Typography variant="body2" sx={{ color: '#6c757d', fontSize: '0.875rem' }}>
            Total
          </Typography>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              color: '#875a7b',
              fontSize: '1.25rem',
            }}
          >
            {formatCurrency(total)}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default OdooSalesOrderLinesTable;
