import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Chip,
  Box,
  TextField,
  IconButton,
  Tooltip,
  Paper,
  Grid,
} from '@mui/material';
import {
  Inventory as StockIcon,
  TrendingUp as InIcon,
  TrendingDown as OutIcon,
  SwapHoriz as TransferIcon,
  Edit as EditIcon,
  Check as ConfirmIcon,
} from '@mui/icons-material';

export interface StockMove {
  id?: number;
  product_id: number;
  product_name: string;
  product_code?: string;
  location_from: string;
  location_to: string;
  quantity_demand: number;
  quantity_done: number;
  quantity_available: number;
  unit_of_measure: string;
  move_type: 'in' | 'out' | 'internal';
  state: 'draft' | 'waiting' | 'confirmed' | 'assigned' | 'done' | 'cancel';
  reference?: string;
  date_expected: string;
  date_done?: string;
}

interface StockMovesTableProps {
  moves: StockMove[];
  onQuantityChange?: (moveId: number, quantity: number) => void;
  onConfirmMove?: (moveId: number) => void;
  editable?: boolean;
  showActions?: boolean;
  title?: string;
}

export const StockMovesTable: React.FC<StockMovesTableProps> = ({
  moves,
  onQuantityChange,
  onConfirmMove,
  editable = false,
  showActions = false,
  title = "Stock Movements",
}) => {
  const getMoveTypeIcon = (moveType: string) => {
    switch (moveType) {
      case 'in':
        return <InIcon color="success" />;
      case 'out':
        return <OutIcon color="error" />;
      case 'internal':
        return <TransferIcon color="info" />;
      default:
        return <StockIcon />;
    }
  };

  const getMoveTypeColor = (moveType: string) => {
    switch (moveType) {
      case 'in':
        return 'success' as const;
      case 'out':
        return 'error' as const;
      case 'internal':
        return 'info' as const;
      default:
        return 'default' as const;
    }
  };

  const getStateColor = (state: string) => {
    const colors = {
      draft: 'default' as const,
      waiting: 'warning' as const,
      confirmed: 'info' as const,
      assigned: 'primary' as const,
      done: 'success' as const,
      cancel: 'error' as const,
    };
    return colors[state as keyof typeof colors] || 'default';
  };

  const getStateLabel = (state: string) => {
    const labels = {
      draft: 'Draft',
      waiting: 'Waiting',
      confirmed: 'Confirmed',
      assigned: 'Ready',
      done: 'Done',
      cancel: 'Cancelled',
    };
    return labels[state as keyof typeof labels] || state;
  };

  const handleQuantityChange = (moveId: number, value: string) => {
    const quantity = parseFloat(value) || 0;
    if (onQuantityChange) {
      onQuantityChange(moveId, quantity);
    }
  };

  const getAvailabilityStatus = (move: StockMove) => {
    if (move.move_type === 'in') return null; // Incoming moves don't need availability check
    
    if (move.quantity_available >= move.quantity_demand) {
      return { label: 'Available', color: 'success' as const };
    } else if (move.quantity_available > 0) {
      return { label: 'Partial', color: 'warning' as const };
    } else {
      return { label: 'Not Available', color: 'error' as const };
    }
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <Box sx={{ p: 2, borderBottom: '1px solid #e0e0e0' }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <StockIcon />
          {title}
        </Typography>
      </Box>
      
      <TableContainer sx={{ maxHeight: 600 }}>
        <Table stickyHeader size="small">
          <TableHead>
            <TableRow>
              <TableCell>Product</TableCell>
              <TableCell>From</TableCell>
              <TableCell>To</TableCell>
              <TableCell align="right">Demand</TableCell>
              <TableCell align="right">Done</TableCell>
              <TableCell align="right">Available</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>State</TableCell>
              <TableCell>Expected Date</TableCell>
              {showActions && <TableCell align="center">Actions</TableCell>}
            </TableRow>
          </TableHead>
          <TableBody>
            {moves.length === 0 ? (
              <TableRow>
                <TableCell colSpan={showActions ? 10 : 9} align="center" sx={{ py: 3 }}>
                  <Typography color="text.secondary">
                    No stock movements found
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              moves.map((move) => {
                const availabilityStatus = getAvailabilityStatus(move);
                
                return (
                  <TableRow key={move.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getMoveTypeIcon(move.move_type)}
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {move.product_code && `[${move.product_code}] `}
                            {move.product_name}
                          </Typography>
                          {move.reference && (
                            <Typography variant="caption" color="text.secondary">
                              Ref: {move.reference}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {move.location_from}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {move.location_to}
                      </Typography>
                    </TableCell>
                    
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="medium">
                        {move.quantity_demand} {move.unit_of_measure}
                      </Typography>
                    </TableCell>
                    
                    <TableCell align="right">
                      {editable && move.state === 'assigned' ? (
                        <TextField
                          size="small"
                          type="number"
                          value={move.quantity_done}
                          onChange={(e) => handleQuantityChange(move.id!, e.target.value)}
                          inputProps={{ 
                            step: '0.01', 
                            min: '0', 
                            max: move.quantity_demand,
                            style: { textAlign: 'right', width: '80px' }
                          }}
                        />
                      ) : (
                        <Typography 
                          variant="body2" 
                          fontWeight="medium"
                          color={move.quantity_done > 0 ? 'success.main' : 'text.secondary'}
                        >
                          {move.quantity_done} {move.unit_of_measure}
                        </Typography>
                      )}
                    </TableCell>
                    
                    <TableCell align="right">
                      <Typography 
                        variant="body2"
                        color={move.quantity_available >= move.quantity_demand ? 'success.main' : 'error.main'}
                      >
                        {move.quantity_available} {move.unit_of_measure}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      {availabilityStatus && (
                        <Chip
                          label={availabilityStatus.label}
                          color={availabilityStatus.color}
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </TableCell>
                    
                    <TableCell>
                      <Chip
                        label={getStateLabel(move.state)}
                        color={getStateColor(move.state)}
                        size="small"
                        sx={{ textTransform: 'capitalize' }}
                      />
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(move.date_expected).toLocaleDateString()}
                      </Typography>
                      {move.date_done && (
                        <Typography variant="caption" color="text.secondary" display="block">
                          Done: {new Date(move.date_done).toLocaleDateString()}
                        </Typography>
                      )}
                    </TableCell>
                    
                    {showActions && (
                      <TableCell align="center">
                        {move.state === 'assigned' && onConfirmMove && (
                          <Tooltip title="Confirm Move">
                            <IconButton
                              size="small"
                              onClick={() => onConfirmMove(move.id!)}
                              disabled={move.quantity_done === 0}
                              color="primary"
                            >
                              <ConfirmIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </TableCell>
                    )}
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* Summary */}
      {moves.length > 0 && (
        <Box sx={{ p: 2, bgcolor: 'grey.50', borderTop: '1px solid #e0e0e0' }}>
          <Grid container spacing={2}>
            <Grid item xs={3}>
              <Typography variant="body2" color="text.secondary">
                Total Moves: {moves.length}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body2" color="text.secondary">
                Completed: {moves.filter(m => m.state === 'done').length}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body2" color="text.secondary">
                Pending: {moves.filter(m => ['draft', 'waiting', 'confirmed', 'assigned'].includes(m.state)).length}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body2" color="text.secondary">
                Cancelled: {moves.filter(m => m.state === 'cancel').length}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      )}
    </Paper>
  );
};

export default StockMovesTable;
