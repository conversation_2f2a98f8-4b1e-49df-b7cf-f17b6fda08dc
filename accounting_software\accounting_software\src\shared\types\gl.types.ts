/**
 * General Ledger Types and Interfaces
 * Following IFRS standards and QuickBooks/Oracle Financials patterns
 */

export interface AccountType {
  id: number;
  name: string;
  description?: string;
  normal_balance: 'DEBIT' | 'CREDIT';
  parent?: number;
}

export interface DetailType {
  id: number;
  code: string;
  name: string;
  description?: string;
  account_type: number;
  account_type_name?: string;
  account_type_code?: string;
  requires_customer: boolean;
  requires_vendor: boolean;
  requires_item: boolean;
  is_tax_account: boolean;
  default_tax_code?: string;
  balance_sheet_classification?: 'CURRENT' | 'NON_CURRENT' | '';
  sort_order: number;
  is_active: boolean;
  accounts_count?: number;
  created_at: string;
  updated_at: string;
}

export interface Account {
  id: number;
  account_number: string;
  account_name: string;
  description?: string;
  account_type: number;
  account_type_name: string;
  detail_type: number;
  detail_type_name: string;
  parent_account?: number;
  is_active: boolean;
  is_header_account: boolean;
  current_balance: string;
  currency: string;
  created_at: string;
  updated_at: string;
}

export interface JournalEntry {
  id: number;
  entry_number: string;
  date: string;
  description: string;
  reference?: string;
  total_debit: string;
  total_credit: string;
  is_posted: boolean;
  created_by: number;
  created_at: string;
  updated_at: string;
  lines: JournalEntryLine[];
}

export interface JournalEntryLine {
  id: number;
  journal_entry: number;
  account: number;
  account_name?: string;
  description?: string;
  debit_amount: string;
  credit_amount: string;
  line_number: number;
  sales_tax?: number;
  sales_tax_description?: string;
  sales_tax_rate?: number;
  sales_tax_amount: string;
  taxable_amount: string;
  memo?: string;
}

export interface GLApiResponse<T> {
  count: number;
  next?: string | null;
  previous?: string | null;
  results: T[];
}

// Odoo-style AccountMove types
export interface AccountMove {
  id: number;
  name: string;
  move_type: 'entry' | 'out_invoice' | 'out_refund' | 'in_invoice' | 'in_refund' | 'out_receipt' | 'in_receipt';
  state: 'draft' | 'posted' | 'cancel';
  ref?: string;
  date: string;
  partner_id?: number;
  partner_name?: string;
  amount_total: string;
  amount_residual: string;
  currency_id: string;
  journal_id: number;
  journal_name?: string;
  company_id?: number;
  sequence_id?: number;
  line_ids: AccountMoveLine[];
  created_uid?: number;
  write_uid?: number;
  create_date: string;
  write_date: string;
}

export interface AccountMoveLine {
  id: number;
  move_id: number;
  account_id: number;
  account_number?: string;
  account_name?: string;
  partner_id?: number;
  partner_name?: string;
  name: string;
  debit: string;
  credit: string;
  balance: string;
  currency_id: string;
  amount_currency: string;
  tax_ids?: number[];
  tax_base_amount: string;
  product_id?: number;
  product_name?: string;
  quantity: string;
  price_unit: string;
  reconciled: boolean;
  full_reconcile_id?: number;
  company_id?: number;
  create_date: string;
  write_date: string;
}

export interface AccountJournal {
  id: number;
  name: string;
  code: string;
  type: 'sale' | 'purchase' | 'cash' | 'bank' | 'general';
  default_account_id: number;
  default_account_name?: string;
  sequence_id?: number;
  active: boolean;
  company_id?: number;
  created_at: string;
  updated_at: string;
}

export interface IrSequence {
  id: number;
  name: string;
  code: string;
  prefix: string;
  suffix: string;
  number_next: number;
  number_increment: number;
  padding: number;
  use_date_range: boolean;
  active: boolean;
  company_id?: number;
  created_at: string;
  updated_at: string;
}

export interface AccountFormData {
  account_number: string;
  account_name: string;
  description: string;
  account_type: number | '';
  detail_type: number | '';
  parent_account: number | '';
  currency: string;
  is_active: boolean;
  is_header_account: boolean;
  opening_balance: string;
  opening_balance_date: string;
  tax_line: string;
  bank_account_number: string;
  bank_routing_number: string;
}

export interface AccountsApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Account[];
}

export interface AccountsFilter {
  account_type?: number;
  search?: string;
  ordering?: string;
} 