// Sales Module Types - Adapted from Purchase Module

export type SalesStatus = 'draft' | 'pending' | 'sent' | 'acknowledged' | 'partial' | 'delivered' | 'closed' | 'cancelled';

export interface SalesLineItem {
  id?: number;
  product?: number;
  product_name?: string;
  description: string;
  quantity: number;
  unit_of_measure?: string;
  unit_price: number;
  discount_percent?: number;
  line_total: number;
  taxable?: boolean;
  tax_rate?: number;
  tax_amount?: number;
  quantity_delivered?: number;
  quantity_pending?: number;
  line_order?: number;
  created_at?: string;
  updated_at?: string;
}

export interface SalesOrder {
  id: number;
  so_id: string;
  so_number: string;
  customer: number;
  customer_name?: string;
  so_date: string;
  expected_date?: string;
  
  // Seller Information
  seller_name?: string;
  seller_email?: string;
  seller_phone?: string;
  
  // Financial Information
  subtotal: number;
  discount_percent: number;
  discount_amount: number;
  tax_amount: number;
  total_amount: number;
  amount_delivered: number;
  balance_due: number;
  
  // Settings
  status: SalesStatus;
  payment_terms?: string;
  
  // Additional Information
  reference_number?: string;
  memo?: string;
  notes?: string;
  ship_to_address?: string;
  
  // Email Tracking
  email_sent?: boolean;
  email_sent_date?: string;
  acknowledged_date?: string;
  
  // Line Items
  lineItems: SalesLineItem[];
  
  // Metadata
  created_at: string;
  updated_at: string;
}

export interface SalesOrderFormData extends Omit<SalesOrder, 'id' | 'so_id' | 'created_at' | 'updated_at'> {}

export interface CustomerBillLineItem {
  id?: number;
  product?: number;
  product_name?: string;
  item_description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  taxable?: boolean;
  tax_rate?: number;
  tax_amount?: number;
  account_code?: string;
  line_order?: number;
  created_at?: string;
  updated_at?: string;
}

export interface CustomerBill {
  id: number;
  bill_id: string;
  bill_number: string;
  customer: number;
  customer_name?: string;
  bill_type: 'bill' | 'credit';
  
  // Dates
  bill_date: string;
  due_date: string;
  
  // Related Documents
  sales_order?: number;
  sales_order_number?: string;
  
  // Financial Information
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  
  // Status and Terms
  status: 'draft' | 'posted' | 'paid';
  payment_terms?: string;
  
  // Additional Information
  reference_number?: string;
  notes?: string;
  
  // Line Items
  lineItems: CustomerBillLineItem[];
  
  // Metadata
  created_at: string;
  updated_at: string;
}

export interface CustomerBillFormData extends Omit<CustomerBill, 'id' | 'bill_id' | 'created_at' | 'updated_at'> {}

// Product interface for sales
export interface SalesProduct {
  id: number;
  name: string;
  description?: string;
  sku?: string;
  unit_of_measure?: string;
  sales_price?: number;
  cost_price?: number;
  taxable?: boolean;
  tax_rate?: number;
  revenue_account_code?: string;
  inventory_account_code?: string;
  cogs_account_code?: string;
  category?: string;
  active?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Customer interface for sales
export interface SalesCustomer {
  id: number;
  display_name: string;
  company_name?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  website?: string;
  
  // Addresses
  billing_address?: string;
  shipping_address?: string;
  
  // Financial Information
  payment_terms?: string;
  credit_limit?: number;
  tax_exempt?: boolean;
  
  // Settings
  active?: boolean;
  
  // Metadata
  created_at?: string;
  updated_at?: string;
}

// Statistics interfaces
export interface SalesOrderStats {
  total_orders: number;
  total_sales: number;
  pending_amount: number;
  delivered_count: number;
  draft_count: number;
  cancelled_count: number;
}

export interface CustomerBillStats {
  total_bills: number;
  total_receivables: number;
  outstanding_amount: number;
  overdue_count: number;
  draft_count: number;
  paid_count: number;
}

// Filter interfaces
export interface SalesOrderFilters {
  status?: string;
  customer?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface CustomerBillFilters {
  status?: string;
  bill_type?: string;
  customer?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

// Form data for creating bills from sales orders
export interface CreateBillFromSOData {
  sales_order: number;
  bill_date: string;
  due_date?: string;
  payment_terms?: string;
  notes?: string;
  line_items?: {
    sales_order_line_item: number;
    quantity?: number;
    unit_price?: number;
  }[];
}

// API Response interfaces
export interface SalesOrderResponse {
  results: SalesOrder[];
  count: number;
  next?: string;
  previous?: string;
}

export interface CustomerBillResponse {
  results: CustomerBill[];
  count: number;
  next?: string;
  previous?: string;
}

// Chart data interfaces
export interface SalesChartData {
  month: string;
  sales: number;
  orders?: number;
}

export interface CustomerBillChartData {
  month: string;
  bills: number;
  amount: number;
}

// Email interfaces
export interface EmailData {
  to: string;
  subject?: string;
  message?: string;
}

// Payment interfaces
export interface PaymentData {
  payment_date?: string;
  payment_method?: string;
  amount?: number;
  reference?: string;
}

// Delivery interfaces
export interface DeliveryData {
  delivery_date?: string;
  notes?: string;
  delivered_items?: {
    line_item_id: number;
    quantity_delivered: number;
  }[];
}
