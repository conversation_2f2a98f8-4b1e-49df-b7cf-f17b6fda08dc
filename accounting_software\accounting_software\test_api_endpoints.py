#!/usr/bin/env python3

import requests
import json

def test_api_endpoints():
    """Test if the API endpoints are working"""
    
    print('🧪 Testing API Endpoints')
    print('=' * 40)
    
    try:
        # Test customer bills endpoint
        print('\n1. Testing Customer Bills API...')
        response = requests.get('http://localhost:8000/api/sales/customer-bills/', timeout=10)
        print(f'   Status: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'   ✅ Customer Bills API is working!')
            if isinstance(data, list):
                print(f'   Found {len(data)} customer bills')
            elif isinstance(data, dict) and 'results' in data:
                print(f'   Found {len(data["results"])} customer bills')
        elif response.status_code == 401:
            print('   ⚠️ Authentication required (this is normal)')
        else:
            print(f'   ❌ Error: {response.status_code}')
            
        # Test GL journal entries endpoint
        print('\n2. Testing GL Journal Entries API...')
        response = requests.get('http://localhost:8000/api/gl/journal-entries/', timeout=10)
        print(f'   Status: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'   ✅ GL Journal Entries API is working!')
            if isinstance(data, list):
                print(f'   Found {len(data)} journal entries')
                cb_entries = [e for e in data if 'CB-' in e.get('reference_number', '')]
                print(f'   Customer Bill entries: {len(cb_entries)}')
            elif isinstance(data, dict) and 'results' in data:
                print(f'   Found {len(data["results"])} journal entries')
                cb_entries = [e for e in data['results'] if 'CB-' in e.get('reference_number', '')]
                print(f'   Customer Bill entries: {len(cb_entries)}')
        elif response.status_code == 401:
            print('   ⚠️ Authentication required (this is normal)')
        else:
            print(f'   ❌ Error: {response.status_code}')
            
        # Test Django admin
        print('\n3. Testing Django Admin...')
        response = requests.get('http://localhost:8000/admin/', timeout=5)
        if response.status_code in [200, 302]:
            print('   ✅ Django admin is accessible')
        else:
            print(f'   ⚠️ Django admin response: {response.status_code}')
            
    except Exception as e:
        print(f'   ❌ Error: {e}')
        return False
        
    return True

if __name__ == "__main__":
    success = test_api_endpoints()
    
    print('\n' + '=' * 40)
    if success:
        print('🎉 API endpoints are accessible!')
    else:
        print('❌ API test failed')
    
    print('\n💡 Next steps:')
    print('1. Open Django admin: http://localhost:8000/admin/')
    print('2. Login with your admin credentials')
    print('3. Go to Sales → Customer Bills')
    print('4. Create a customer bill and set status to "posted"')
    print('5. Check GL → Journal Entries for the created entries')
    print('6. Test the frontend customer bills page')
