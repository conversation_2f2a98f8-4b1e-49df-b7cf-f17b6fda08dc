#!/usr/bin/env python3
"""
Quick test to check if Django backend is working
"""

import requests
import json

def test_backend():
    print("🔍 Testing Django Backend...")
    
    # Test 1: Check if server is responding
    try:
        response = requests.get("http://localhost:8000/api/", timeout=5)
        print(f"✅ Server responding: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Backend is working!")
            return True
        else:
            print(f"❌ Backend error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend - Django server not running!")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_delivery_notes_api():
    print("\n🔍 Testing Delivery Notes API...")
    
    try:
        # Test without authentication first
        response = requests.get("http://localhost:8000/api/sales/delivery-notes/", timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 401:
            print("❌ Authentication required - need to login first")
            return False
        elif response.status_code == 200:
            print("✅ Delivery notes API working!")
            data = response.json()
            print(f"Found {len(data.get('results', []))} delivery notes")
            return True
        else:
            print(f"❌ API error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Backend Test")
    print("=" * 30)
    
    backend_ok = test_backend()
    if backend_ok:
        test_delivery_notes_api()
    
    print("\n✅ Test completed!")
