#!/usr/bin/env python3

import os
import sys
import django
from decimal import Decimal

# Add the erp_backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'erp_backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

def test_customer_bill_gl_integration():
    """Test customer bill creation and GL integration"""
    
    print("🧪 Testing Customer Bill GL Integration")
    print("=" * 50)
    
    try:
        # Import models
        from sales.models import CustomerBill, CustomerBillItem
        from contacts.models import Contact
        from gl.models import JournalEntry
        from django.contrib.auth.models import User
        
        # Get or create a test user
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={'email': '<EMAIL>', 'first_name': 'Test', 'last_name': 'User'}
        )
        if created:
            user.set_password('testpass')
            user.save()
            print("✅ Created test user")
        else:
            print("✅ Using existing test user")
        
        # Get or create a test customer
        try:
            customer = Contact.objects.filter(name='Test Customer').first()
            if not customer:
                customer = Contact.objects.create(
                    name='Test Customer GL Test',
                    email='<EMAIL>',
                    phone='************',
                    contact_type='customer'
                )
                created = True
            else:
                created = False
        except Exception as e:
            print(f"Error with customer: {e}")
            # Use the first available contact
            customer = Contact.objects.first()
            created = False
        if created:
            print("✅ Created test customer")
        else:
            print("✅ Using existing test customer")
        
        # Check existing customer bills
        existing_bills = CustomerBill.objects.count()
        print(f"📊 Existing customer bills: {existing_bills}")
        
        # Check existing journal entries
        existing_entries = JournalEntry.objects.count()
        print(f"📊 Existing journal entries: {existing_entries}")
        
        # Create a test customer bill
        print("\n🔨 Creating test customer bill...")
        customer_bill = CustomerBill.objects.create(
            bill_number='TEST-BILL-001',
            customer=customer,
            bill_type='bill',
            bill_date='2024-07-11',
            due_date='2024-08-11',
            subtotal=Decimal('1000.00'),
            tax_amount=Decimal('100.00'),
            total_amount=Decimal('1100.00'),
            amount_paid=Decimal('0.00'),
            balance_due=Decimal('1100.00'),
            status='draft',
            reference_number='TEST-REF-001',
            notes='Test customer bill for GL integration',
            created_by=user
        )
        print(f"✅ Created customer bill: {customer_bill.bill_number}")
        print(f"   Status: {customer_bill.status}")
        print(f"   Total: ${customer_bill.total_amount}")
        
        # Check if any journal entries were created (should be none for draft)
        draft_entries = JournalEntry.objects.filter(
            reference_number__icontains=customer_bill.bill_number
        ).count()
        print(f"📊 Journal entries for draft bill: {draft_entries}")
        
        # Now post the bill
        print("\n📮 Posting customer bill...")
        customer_bill.status = 'posted'
        customer_bill.save()  # This should trigger GL entry creation
        
        print(f"✅ Posted customer bill: {customer_bill.bill_number}")
        print(f"   Status: {customer_bill.status}")
        
        # Check if journal entries were created
        posted_entries = JournalEntry.objects.filter(
            reference_number__icontains=customer_bill.bill_number
        )
        print(f"📊 Journal entries for posted bill: {posted_entries.count()}")
        
        if posted_entries.exists():
            for entry in posted_entries:
                print(f"   ✅ Journal Entry: {entry.entry_number}")
                print(f"      Reference: {entry.reference_number}")
                print(f"      Amount: ${entry.total_amount}")
                print(f"      Status: {entry.status}")
                
                # Show journal lines
                lines = entry.journal_lines.all()
                print(f"      Lines: {lines.count()}")
                for line in lines:
                    if line.debit_amount > 0:
                        print(f"        DEBIT  ${line.debit_amount} - {line.account.account_name if line.account else 'No Account'}")
                    if line.credit_amount > 0:
                        print(f"        CREDIT ${line.credit_amount} - {line.account.account_name if line.account else 'No Account'}")
        else:
            print("   ❌ No journal entries created!")
            print("   This indicates the GL integration is not working")
        
        # Summary
        print(f"\n📈 Summary:")
        print(f"   Customer Bills: {CustomerBill.objects.count()}")
        print(f"   Journal Entries: {JournalEntry.objects.count()}")
        print(f"   Posted Bills: {CustomerBill.objects.filter(status='posted').count()}")
        
        return posted_entries.exists()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_customer_bill_gl_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Customer Bill GL Integration is working!")
        print("✅ Customer bills create journal entries when posted")
        print("✅ Journal entries should now appear in the GL module")
    else:
        print("❌ Customer Bill GL Integration is not working")
        print("🔧 Check the CustomerBill.create_gl_entries() method")
        print("🔧 Ensure GL accounts are properly configured")
    
    print("\n💡 Next steps:")
    print("1. Check the frontend customer bills page")
    print("2. Try posting a bill from the frontend")
    print("3. Check the GL Journal Entries page")
