#!/usr/bin/env python3

import requests
import json
import sys

def test_customer_bills_endpoint():
    """Test if the customer bills endpoint is working"""
    
    print("🧪 Testing Customer Bills Backend Endpoint")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # Test 1: Check if customer bills endpoint exists
        print("\n1. Testing customer bills endpoint...")
        response = requests.get(f"{base_url}/api/sales/customer-bills/", timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Endpoint exists and working")
            print(f"   Response: {json.dumps(data, indent=2)[:300]}...")
        elif response.status_code == 401:
            print(f"   ⚠️ Authentication required")
            print(f"   Response: {response.text[:200]}")
        else:
            print(f"   ❌ Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            
        # Test 2: Check if GL journal entries endpoint exists
        print("\n2. Testing GL journal entries endpoint...")
        response = requests.get(f"{base_url}/api/gl/journal-entries/", timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ GL endpoint exists and working")
            if isinstance(data, list):
                print(f"   Found {len(data)} journal entries")
            elif isinstance(data, dict) and 'results' in data:
                print(f"   Found {len(data['results'])} journal entries")
        elif response.status_code == 401:
            print(f"   ⚠️ Authentication required for GL endpoint")
        else:
            print(f"   ❌ GL endpoint error: {response.status_code}")
            
        # Test 3: Check server health
        print("\n3. Testing server health...")
        response = requests.get(f"{base_url}/admin/", timeout=5)
        if response.status_code in [200, 302]:
            print(f"   ✅ Django server is running")
        else:
            print(f"   ⚠️ Django server response: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Cannot connect to backend server")
        print("   Make sure Django server is running on http://localhost:8000")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False
        
    return True

if __name__ == "__main__":
    success = test_customer_bills_endpoint()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Backend connectivity test completed!")
        print("Check the results above to see if endpoints are working.")
    else:
        print("❌ Backend test failed - server might not be running")
    
    print("\n💡 Next steps:")
    print("1. Make sure Django server is running: python manage.py runserver")
    print("2. Check if you can access customer bills in the frontend")
    print("3. Try creating and posting a customer bill")
    print("4. Check if journal entries appear in GL module")
