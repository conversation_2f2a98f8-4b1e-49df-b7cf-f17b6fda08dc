<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend API</title>
</head>
<body>
    <h1>Test Frontend API</h1>
    <button onclick="testAuth()">Test Authentication</button>
    <button onclick="testCreateDeliveryNote()">Test Create Delivery Note</button>
    <button onclick="testCurrentToken()">Test Current Token</button>
    <div id="output"></div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let authToken = null;

        function log(message) {
            const output = document.getElementById('output');
            output.innerHTML += '<p>' + JSON.stringify(message, null, 2) + '</p>';
        }

        async function testAuth() {
            try {
                const response = await fetch(`${API_BASE}/../api-token-auth/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.token;
                    log('Auth successful: ' + authToken);
                } else {
                    log('Auth failed: ' + response.status + ' - ' + await response.text());
                }
            } catch (error) {
                log('Auth error: ' + error.message);
            }
        }

        async function testCurrentToken() {
            const token = localStorage.getItem('token');
            if (!token) {
                log('❌ No token found in localStorage');
                return;
            }

            log('Token found: ' + token.substring(0, 20) + '...');

            try {
                const response = await fetch(`${API_BASE}/sales/sales-orders/`, {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log('Token test status: ' + response.status);
                if (response.status === 401) {
                    log('❌ Token is invalid or expired');
                } else if (response.status === 200) {
                    log('✅ Token is valid');
                } else {
                    log('⚠️ Unexpected status: ' + response.status);
                }
            } catch (error) {
                log('❌ Error testing token: ' + error.message);
            }
        }

        async function testCreateDeliveryNote() {
            if (!authToken) {
                log('Please authenticate first');
                return;
            }

            try {
                // First get a sales order
                const soResponse = await fetch(`${API_BASE}/sales/sales-orders/`, {
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!soResponse.ok) {
                    log('Failed to get sales orders: ' + soResponse.status);
                    return;
                }

                const soData = await soResponse.json();
                if (!soData.results || soData.results.length === 0) {
                    log('No sales orders found');
                    return;
                }

                const salesOrder = soData.results[0];
                log('Using sales order: ' + salesOrder.id);

                // Create delivery note
                const deliveryNoteData = {
                    sales_order: salesOrder.id,
                    customer: salesOrder.customer_id || salesOrder.customer,
                    delivery_date: new Date().toISOString().split('T')[0],
                    expected_delivery_date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
                    delivery_address: 'Test Address from HTML',
                    delivery_contact_person: '',
                    delivery_contact_phone: '',
                    vehicle_number: '',
                    driver_name: '',
                    driver_phone: '',
                    notes: '',
                    internal_notes: '',
                    status: 'draft'
                };

                log('Sending delivery note data: ' + JSON.stringify(deliveryNoteData, null, 2));

                const response = await fetch(`${API_BASE}/sales/delivery-notes/`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(deliveryNoteData)
                });

                log('Response status: ' + response.status);
                log('Response headers: ' + JSON.stringify(Object.fromEntries(response.headers.entries())));

                const responseText = await response.text();
                log('Response body: ' + responseText);

                if (response.ok) {
                    log('✅ Delivery note created successfully!');
                } else {
                    log('❌ Failed to create delivery note');
                }

            } catch (error) {
                log('Error creating delivery note: ' + error.message);
            }
        }
    </script>
</body>
</html>
