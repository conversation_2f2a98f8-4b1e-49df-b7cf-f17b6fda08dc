<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Auth Test</title>
</head>
<body>
    <h1>Frontend Authentication Test</h1>
    <div id="results"></div>
    
    <script>
        async function testFrontendAuth() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing frontend authentication...</p>';
            
            try {
                // Check if token exists in localStorage
                const token = localStorage.getItem('token');
                console.log('Token from localStorage:', token);
                
                if (!token) {
                    resultsDiv.innerHTML = `
                        <h2>❌ No Authentication Token</h2>
                        <p>No token found in localStorage. Please login first.</p>
                        <p><a href="http://localhost:5174/login">Go to Login</a></p>
                    `;
                    return;
                }
                
                // Test products endpoint with the token
                const productsResponse = await fetch('http://localhost:8000/api/sales/products/', {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                console.log('Products response status:', productsResponse.status);
                
                if (!productsResponse.ok) {
                    const errorText = await productsResponse.text();
                    resultsDiv.innerHTML = `
                        <h2>❌ Products API Failed</h2>
                        <p><strong>Status:</strong> ${productsResponse.status}</p>
                        <p><strong>Error:</strong> ${errorText}</p>
                        <p>Token might be expired or invalid.</p>
                    `;
                    return;
                }
                
                const productsData = await productsResponse.json();
                
                resultsDiv.innerHTML = `
                    <h2>✅ Frontend Authentication Working</h2>
                    <p><strong>Token:</strong> ${token.substring(0, 20)}...</p>
                    <p><strong>Products API Status:</strong> ${productsResponse.status}</p>
                    <p><strong>Products Count:</strong> ${productsData.results ? productsData.results.length : 'N/A'}</p>
                    <p><strong>Total Products:</strong> ${productsData.count || 'N/A'}</p>
                    
                    <h3>First Product:</h3>
                    <pre>${JSON.stringify(productsData.results?.[0] || 'No products', null, 2)}</pre>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h2>❌ Error</h2>
                    <p>${error.message}</p>
                `;
                console.error('Test failed:', error);
            }
        }
        
        // Run test when page loads
        window.onload = testFrontendAuth;
    </script>
</body>
</html>
