<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Products Test</title>
</head>
<body>
    <h1>Frontend Products API Test</h1>
    <div id="results"></div>
    
    <script>
        async function testProductsAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing products API...</p>';
            
            try {
                // Get auth token
                const authResponse = await fetch('http://127.0.0.1:8000/api-token-auth/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                if (!authResponse.ok) {
                    throw new Error(`Auth failed: ${authResponse.status}`);
                }
                
                const authData = await authResponse.json();
                const token = authData.token;
                
                // Test products endpoint
                const productsResponse = await fetch('http://127.0.0.1:8000/api/sales/products/', {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                if (!productsResponse.ok) {
                    throw new Error(`Products API failed: ${productsResponse.status}`);
                }
                
                const productsData = await productsResponse.json();
                
                resultsDiv.innerHTML = `
                    <h2>✅ Products API Test Results</h2>
                    <p><strong>Status:</strong> ${productsResponse.status}</p>
                    <p><strong>Response Type:</strong> ${typeof productsData}</p>
                    <p><strong>Has Results:</strong> ${productsData.hasOwnProperty('results')}</p>
                    <p><strong>Products Count:</strong> ${productsData.results ? productsData.results.length : 'N/A'}</p>
                    <p><strong>Total Count:</strong> ${productsData.count || 'N/A'}</p>
                    
                    <h3>First Product:</h3>
                    <pre>${JSON.stringify(productsData.results?.[0] || 'No products', null, 2)}</pre>
                    
                    <h3>Full Response:</h3>
                    <pre>${JSON.stringify(productsData, null, 2)}</pre>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h2>❌ Error</h2>
                    <p>${error.message}</p>
                `;
                console.error('Test failed:', error);
            }
        }
        
        // Run test when page loads
        window.onload = testProductsAPI;
    </script>
</body>
</html>
