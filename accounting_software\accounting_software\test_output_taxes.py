#!/usr/bin/env python3
import requests

# Test output taxes API
try:
    auth_response = requests.post('http://127.0.0.1:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code == 200:
        token = auth_response.json().get('token')
        headers = {'Authorization': f'Token {token}'}
        
        print("Testing output taxes API:")
        response = requests.get('http://127.0.0.1:8000/api/sales-tax/?tax_type=output', headers=headers)
        if response.status_code == 200:
            data = response.json()
            taxes = data.get('results', data) if isinstance(data, dict) else data
            print(f'Output taxes found: {len(taxes)}')
            
            for tax in taxes:
                print(f'  - ID: {tax.get("id")}, Description: {tax.get("description")}, Type: {tax.get("tax_type", "N/A")}, Rate: {tax.get("rate")}%')
        else:
            print(f'Error: {response.status_code} - {response.text}')
            
        print("\nTesting all taxes API:")
        response = requests.get('http://127.0.0.1:8000/api/sales-tax/', headers=headers)
        if response.status_code == 200:
            data = response.json()
            taxes = data.get('results', data) if isinstance(data, dict) else data
            print(f'All taxes found: {len(taxes)}')
            
            for tax in taxes:
                print(f'  - ID: {tax.get("id")}, Description: {tax.get("description")}, Type: {tax.get("tax_type", "N/A")}, Rate: {tax.get("rate")}%')
    else:
        print('Auth failed')
        
except Exception as e:
    print(f'Error: {e}')
