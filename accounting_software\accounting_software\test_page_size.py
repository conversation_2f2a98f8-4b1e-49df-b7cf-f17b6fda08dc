#!/usr/bin/env python3
import requests

# Test with page_size parameter
try:
    auth_response = requests.post('http://127.0.0.1:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code == 200:
        token = auth_response.json().get('token')
        headers = {'Authorization': f'Token {token}'}
        
        print("Testing default pagination:")
        response = requests.get('http://127.0.0.1:8000/api/sales/products/', headers=headers)
        if response.status_code == 200:
            data = response.json()
            products = data.get('results', data)
            print(f'Default: {len(products)} out of {data.get("count", "unknown")} total')
        
        print("\nTesting with page_size=1000:")
        response = requests.get('http://127.0.0.1:8000/api/sales/products/?page_size=1000', headers=headers)
        if response.status_code == 200:
            data = response.json()
            products = data.get('results', data)
            print(f'Large page: {len(products)} out of {data.get("count", "unknown")} total')
            print(f'Has next page: {data.get("next") is not None}')
        else:
            print(f'Error with page_size: {response.text}')
    else:
        print('Auth failed')
        
except Exception as e:
    print(f'Error: {e}')
