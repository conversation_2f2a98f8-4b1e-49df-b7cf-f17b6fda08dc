#!/usr/bin/env python3
"""
Debug script to test products API and understand the response structure
"""
import requests
import json

def test_products_api():
    print("🔍 Testing Products API for Sales Order Debug...")
    print("=" * 60)
    
    try:
        # Get auth token
        auth_response = requests.post('http://127.0.0.1:8000/api-token-auth/', {
            'username': 'admin',
            'password': 'admin123'
        })
        
        if auth_response.status_code != 200:
            print(f"❌ Authentication failed: {auth_response.status_code}")
            return False
            
        token = auth_response.json().get('token')
        headers = {'Authorization': f'Token {token}'}
        print("✅ Authentication successful")
        
        # Test products endpoint
        response = requests.get('http://127.0.0.1:8000/api/sales/products/', headers=headers)
        print(f"\n📦 Products API Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response type: {type(data)}")
            
            if isinstance(data, dict):
                print(f"Response keys: {list(data.keys())}")
                
                # Check if it's paginated
                if 'results' in data:
                    products = data['results']
                    print(f"✅ Paginated response - Number of products: {len(products)}")
                    print(f"Total count: {data.get('count', 'N/A')}")
                else:
                    products = data
                    print(f"✅ Direct response - Number of products: {len(products)}")
            else:
                products = data
                print(f"✅ Array response - Number of products: {len(products)}")
            
            if products:
                print(f"\n🔍 First Product Analysis:")
                first_product = products[0]
                print(f"  ID: {first_product.get('id')}")
                print(f"  Name: {first_product.get('name')}")
                print(f"  SKU: {first_product.get('sku')}")
                print(f"  Product Type: {first_product.get('product_type')}")
                print(f"  Unit Price: {first_product.get('unit_price')} (type: {type(first_product.get('unit_price'))})")
                print(f"  Cost Price: {first_product.get('cost_price')}")
                print(f"  Status: {first_product.get('status')}")
                print(f"  Taxable: {first_product.get('taxable')}")
                print(f"  Stock: {first_product.get('quantity_on_hand')}")
                
                print(f"\n📋 All Product Fields:")
                for key, value in first_product.items():
                    print(f"  {key}: {value} ({type(value).__name__})")
                
                # Test if products are active
                active_products = [p for p in products if p.get('status') == 'active']
                print(f"\n✅ Active Products: {len(active_products)} out of {len(products)}")
                
                return True
            else:
                print("❌ No products found in response")
                return False
        else:
            print(f"❌ Products API failed: {response.status_code}")
            print(f"Error: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_products_api()
    if success:
        print("\n🎉 Products API test completed successfully!")
    else:
        print("\n💥 Products API test failed!")
