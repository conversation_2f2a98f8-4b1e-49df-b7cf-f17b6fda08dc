#!/usr/bin/env python3
"""
Test script to check Sales API endpoints
"""
import requests
import json
import sys

def test_sales_api():
    base_url = 'http://127.0.0.1:8000/api'
    
    print("🔍 Testing Sales API Endpoints...")
    print("=" * 50)
    
    # Get auth token first
    try:
        auth_response = requests.post(f'{base_url}-token-auth/', {
            'username': 'admin',
            'password': 'admin123'
        })

        if auth_response.status_code != 200:
            print(f"❌ Authentication failed: {auth_response.status_code}")
            print(f"Response: {auth_response.text}")
            return False

        token = auth_response.json().get('token')
        if not token:
            print("❌ No access token received")
            return False

        headers = {'Authorization': f'Token {token}'}
        print("✅ Authentication successful")
        
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return False
    
    # Test sales endpoints
    sales_endpoints = [
        ('/sales/categories/', 'Product Categories'),
        ('/sales/products/', 'Products'),
        ('/sales/payment-terms/', 'Payment Terms'),
        ('/sales/sales-orders/', 'Sales Orders'),
        ('/sales/delivery-notes/', 'Delivery Notes'),
        ('/sales/customer-invoices/', 'Customer Invoices'),
        ('/sales/return-notes/', 'Return Notes'),
        ('/sales/product-pricing/', 'Product Pricing')
    ]
    
    print("\n📊 Testing Sales Endpoints:")
    print("-" * 30)
    
    all_passed = True
    
    for endpoint, name in sales_endpoints:
        try:
            response = requests.get(base_url + endpoint, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                count = len(data.get('results', data)) if isinstance(data, dict) else len(data)
                print(f"✅ {name}: {response.status_code} - {count} items")
            else:
                print(f"❌ {name}: {response.status_code}")
                print(f"   Error: {response.text[:100]}...")
                all_passed = False
                
        except Exception as e:
            print(f"❌ {name}: ERROR - {str(e)}")
            all_passed = False
    
    # Test contacts endpoints (customers)
    print("\n👥 Testing Customer Endpoints:")
    print("-" * 30)
    
    try:
        response = requests.get(f'{base_url}/contacts/customers/', headers=headers)
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('results', data)) if isinstance(data, dict) else len(data)
            print(f"✅ Customers: {response.status_code} - {count} items")
        else:
            print(f"❌ Customers: {response.status_code}")
            print(f"   Error: {response.text[:100]}...")
            all_passed = False
    except Exception as e:
        print(f"❌ Customers: ERROR - {str(e)}")
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All Sales API endpoints are working!")
        return True
    else:
        print("⚠️  Some Sales API endpoints have issues")
        return False

if __name__ == "__main__":
    success = test_sales_api()
    sys.exit(0 if success else 1)
