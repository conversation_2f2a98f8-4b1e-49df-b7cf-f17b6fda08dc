#!/usr/bin/env python3
"""
Comprehensive test script for Sales Module Integration
Tests both backend API and frontend-backend connectivity
"""
import requests
import json
import sys
from datetime import datetime, timed<PERSON><PERSON>

def get_auth_token():
    """Get authentication token"""
    try:
        response = requests.post('http://127.0.0.1:8000/api-token-auth/', {
            'username': 'admin',
            'password': 'admin123'
        })
        
        if response.status_code == 200:
            return response.json().get('token')
        else:
            print(f"❌ Auth failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Auth error: {e}")
        return None

def test_sales_workflow():
    """Test complete sales workflow"""
    print("🔄 Testing Sales Workflow...")
    print("=" * 50)
    
    token = get_auth_token()
    if not token:
        return False
    
    headers = {'Authorization': f'Token {token}', 'Content-Type': 'application/json'}
    base_url = 'http://127.0.0.1:8000/api'
    
    try:
        # 1. Test Customer Management
        print("\n👥 Testing Customer Management:")
        customers_response = requests.get(f'{base_url}/contacts/customers/', headers=headers)
        if customers_response.status_code == 200:
            customers = customers_response.json().get('results', [])
            print(f"✅ Customers loaded: {len(customers)} customers")
            
            if customers:
                customer_id = customers[0]['id']
                print(f"✅ Using customer ID: {customer_id}")
            else:
                print("⚠️  No customers found, creating test customer...")
                # Create a test customer
                customer_data = {
                    'display_name': 'Test Customer',
                    'contact_type': 'customer',
                    'email': '<EMAIL>',
                    'phone': '************'
                }
                create_response = requests.post(f'{base_url}/contacts/customers/', 
                                              json=customer_data, headers=headers)
                if create_response.status_code == 201:
                    customer_id = create_response.json()['id']
                    print(f"✅ Test customer created with ID: {customer_id}")
                else:
                    print(f"❌ Failed to create customer: {create_response.text}")
                    return False
        else:
            print(f"❌ Failed to load customers: {customers_response.text}")
            return False
        
        # 2. Test Products
        print("\n📦 Testing Products:")
        products_response = requests.get(f'{base_url}/sales/products/', headers=headers)
        if products_response.status_code == 200:
            products = products_response.json().get('results', [])
            print(f"✅ Products loaded: {len(products)} products")
            
            if products:
                product_id = products[0]['id']
                product_name = products[0]['name']
                print(f"✅ Using product: {product_name} (ID: {product_id})")
            else:
                print("⚠️  No products found")
                return False
        else:
            print(f"❌ Failed to load products: {products_response.text}")
            return False
        
        # 3. Test Sales Order Creation
        print("\n📋 Testing Sales Order Creation:")
        so_data = {
            'customer': customer_id,
            'so_date': datetime.now().strftime('%Y-%m-%d'),
            'expected_date': (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d'),
            'status': 'draft',
            'notes': 'Test sales order from integration test',
            'lineItems': [
                {
                    'product': product_id,
                    'item_description': f'Test item - {product_name}',
                    'quantity': 2,
                    'unit_price': 100.00,
                    'line_total': 200.00,
                    'taxable': True,
                    'tax_rate': 10.00,
                    'tax_amount': 20.00
                }
            ]
        }
        
        so_response = requests.post(f'{base_url}/sales/sales-orders/', 
                                   json=so_data, headers=headers)
        if so_response.status_code == 201:
            sales_order = so_response.json()
            so_id = sales_order['id']
            print(f"✅ Sales Order created: {sales_order.get('so_number', so_id)}")
        else:
            print(f"❌ Failed to create sales order: {so_response.text}")
            return False
        
        # 4. Test Sales Order Retrieval
        print("\n📊 Testing Sales Order Retrieval:")
        get_so_response = requests.get(f'{base_url}/sales/sales-orders/{so_id}/', headers=headers)
        if get_so_response.status_code == 200:
            retrieved_so = get_so_response.json()
            print(f"✅ Sales Order retrieved: {retrieved_so.get('so_number')}")
            print(f"   Status: {retrieved_so.get('status')}")
            print(f"   Total: ${retrieved_so.get('total_amount', 0)}")
        else:
            print(f"❌ Failed to retrieve sales order: {get_so_response.text}")
            return False
        
        # 5. Test Customer Invoices
        print("\n🧾 Testing Customer Invoices:")
        invoices_response = requests.get(f'{base_url}/sales/customer-invoices/', headers=headers)
        if invoices_response.status_code == 200:
            invoices = invoices_response.json().get('results', [])
            print(f"✅ Customer Invoices loaded: {len(invoices)} invoices")
        else:
            print(f"❌ Failed to load customer invoices: {invoices_response.text}")
            return False
        
        # 6. Test Delivery Notes
        print("\n🚚 Testing Delivery Notes:")
        delivery_notes_response = requests.get(f'{base_url}/sales/delivery-notes/', headers=headers)
        if delivery_notes_response.status_code == 200:
            delivery_notes = delivery_notes_response.json().get('results', [])
            print(f"✅ Delivery Notes loaded: {len(delivery_notes)} notes")
        else:
            print(f"❌ Failed to load delivery notes: {delivery_notes_response.text}")
            return False
        
        # 7. Test Product Pricing
        print("\n💰 Testing Product Pricing:")
        pricing_response = requests.get(f'{base_url}/sales/product-pricing/', headers=headers)
        if pricing_response.status_code == 200:
            pricing = pricing_response.json().get('results', [])
            print(f"✅ Product Pricing loaded: {len(pricing)} price entries")
        else:
            print(f"❌ Failed to load product pricing: {pricing_response.text}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 Sales Module Integration Test PASSED!")
        print("✅ All sales workflow components are working correctly")
        return True
        
    except Exception as e:
        print(f"\n❌ Integration test failed with error: {str(e)}")
        return False

def test_frontend_endpoints():
    """Test endpoints that frontend specifically uses"""
    print("\n🌐 Testing Frontend-Specific Endpoints...")
    print("-" * 40)
    
    token = get_auth_token()
    if not token:
        return False
    
    headers = {'Authorization': f'Token {token}'}
    base_url = 'http://127.0.0.1:8000/api'
    
    # Frontend endpoints
    frontend_endpoints = [
        ('/account/user/', 'Current User'),
        ('/account/company/current/', 'Current Company'),
        ('/contacts/customers/?page=1&page_size=10', 'Paginated Customers'),
        ('/sales/categories/', 'Product Categories'),
        ('/sales/payment-terms/', 'Payment Terms'),
    ]
    
    all_passed = True
    for endpoint, name in frontend_endpoints:
        try:
            response = requests.get(base_url + endpoint, headers=headers)
            if response.status_code == 200:
                print(f"✅ {name}: Working")
            else:
                print(f"❌ {name}: Failed ({response.status_code})")
                all_passed = False
        except Exception as e:
            print(f"❌ {name}: Error - {str(e)}")
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("🧪 Sales Module Integration Test")
    print("Testing backend API and frontend connectivity...")
    print()
    
    # Test backend workflow
    workflow_success = test_sales_workflow()
    
    # Test frontend endpoints
    frontend_success = test_frontend_endpoints()
    
    print("\n" + "=" * 60)
    if workflow_success and frontend_success:
        print("🎉 ALL TESTS PASSED - Sales Module is fully functional!")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED - Check the issues above")
        sys.exit(1)
