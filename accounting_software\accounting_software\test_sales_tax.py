#!/usr/bin/env python3
import requests

# Test sales tax API
try:
    auth_response = requests.post('http://127.0.0.1:8000/api-token-auth/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if auth_response.status_code == 200:
        token = auth_response.json().get('token')
        headers = {'Authorization': f'Token {token}'}
        
        print("Testing sales tax API:")
        response = requests.get('http://127.0.0.1:8000/api/sales-tax/', headers=headers)
        if response.status_code == 200:
            data = response.json()
            taxes = data.get('results', data) if isinstance(data, dict) else data
            print(f'Total sales taxes: {len(taxes)}')
            
            for tax in taxes:
                print(f'  - ID: {tax.get("id")}, Description: {tax.get("description")}, Type: {tax.get("type", "N/A")}, Rate: {tax.get("rate")}%')
                
            # Filter for output taxes
            output_taxes = [tax for tax in taxes if 
                           tax.get('type') == 'output' or 
                           'output' in tax.get('description', '').lower() or
                           'sales' in tax.get('description', '').lower()]
            
            print(f'\nOutput/Sales taxes found: {len(output_taxes)}')
            for tax in output_taxes:
                print(f'  - {tax.get("description")} ({tax.get("rate")}%)')
        else:
            print(f'Error: {response.status_code} - {response.text}')
    else:
        print('Auth failed')
        
except Exception as e:
    print(f'Error: {e}')
