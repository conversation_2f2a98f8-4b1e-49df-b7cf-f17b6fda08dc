#!/usr/bin/env python3

import os
import sys
import django
from decimal import Decimal

# Add the erp_backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'erp_backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

def test_simple_customer_bill():
    """Simple test to check if customer bills can be created and posted"""
    
    print("🧪 Simple Customer Bill Test")
    print("=" * 40)
    
    try:
        # Import models
        from sales.models import CustomerBill, CustomerBillItem
        from contacts.models import Contact
        from gl.models import JournalEntry, Account
        from django.contrib.auth.models import User
        
        # Get first available user and customer
        user = User.objects.first()
        customer = Contact.objects.first()
        
        if not user:
            print("❌ No users found in database")
            return False
            
        if not customer:
            print("❌ No customers found in database")
            return False
            
        print(f"✅ Using user: {user.username}")
        print(f"✅ Using customer: {customer.name}")
        
        # Check if GL accounts exist
        accounts_count = Account.objects.count()
        print(f"📊 GL Accounts available: {accounts_count}")
        
        if accounts_count == 0:
            print("⚠️ No GL accounts found - this might cause issues")
        
        # Create a simple customer bill with unique number
        import time
        unique_id = int(time.time())
        bill_number = f'SIMPLE-TEST-{unique_id}'

        print(f"\n🔨 Creating customer bill {bill_number}...")
        customer_bill = CustomerBill.objects.create(
            bill_number=bill_number,
            customer=customer,
            bill_type='bill',
            bill_date='2024-07-11',
            due_date='2024-08-11',
            subtotal=Decimal('100.00'),
            tax_amount=Decimal('10.00'),
            total_amount=Decimal('110.00'),
            amount_paid=Decimal('0.00'),
            balance_due=Decimal('110.00'),
            status='draft',
            reference_number=f'SIMPLE-REF-{unique_id}',
            notes='Simple test customer bill',
            created_by=user
        )
        print(f"✅ Created bill: {customer_bill.bill_number} (Status: {customer_bill.status})")
        
        # Check journal entries before posting
        before_count = JournalEntry.objects.count()
        print(f"📊 Journal entries before posting: {before_count}")
        
        # Post the bill
        print("\n📮 Posting customer bill...")
        customer_bill.status = 'posted'
        customer_bill.save()
        
        print(f"✅ Posted bill: {customer_bill.bill_number} (Status: {customer_bill.status})")
        
        # Check journal entries after posting
        after_count = JournalEntry.objects.count()
        print(f"📊 Journal entries after posting: {after_count}")
        
        if after_count > before_count:
            print(f"🎉 SUCCESS! {after_count - before_count} journal entry(ies) created!")
            
            # Show the created entries
            new_entries = JournalEntry.objects.filter(
                reference_number__icontains=customer_bill.bill_number
            )
            for entry in new_entries:
                print(f"   📝 Entry: {entry.entry_number}")
                print(f"      Reference: {entry.reference_number}")
                print(f"      Status: {entry.status}")
                # Note: JournalEntry doesn't have total_amount field
                
                lines = entry.journal_lines.all()
                print(f"      Lines: {lines.count()}")
                for line in lines:
                    if line.debit_amount > 0:
                        print(f"        DEBIT  ${line.debit_amount} - {line.account.account_name if line.account else 'No Account'}")
                    if line.credit_amount > 0:
                        print(f"        CREDIT ${line.credit_amount} - {line.account.account_name if line.account else 'No Account'}")
            
            return True
        else:
            print("❌ No journal entries were created!")
            print("   This means the GL integration is not working")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_customer_bill()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 Customer Bill GL Integration WORKS!")
        print("✅ Bills create journal entries when posted")
    else:
        print("❌ Customer Bill GL Integration FAILED")
        print("🔧 Check the create_gl_entries method")
    
    print("\n💡 If this works, the issue might be:")
    print("1. Frontend not calling the correct API")
    print("2. Authentication issues")
    print("3. Missing line items in bills")
    print("4. Missing GL accounts")
